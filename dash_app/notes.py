
# app.py
from dash import Dash, html, dcc
app = Dash(__name__)
app.layout = html.Div(
    style={"height":"100vh","display":"grid","gridTemplateRows":"1fr auto","background":"#0e1116"},
    children=[
        html.Iframe(
            src="assets/tv_click_lines.html",
            style={"width":"100%","height":"100%","border":"0"}
        ),
        html.Div("Prices will also log in the browser console.", style={"color":"#8b949e","padding":"6px 10px"})
    ]
)
if __name__ == "__main__":
    app.run(debug=False)






# from dash_extensions.enrich import DashProxy, Output, Input, State, MATCH, Trigger
# import dash_bootstrap_components as dbc
# from dash import html, dcc, ctx
# from dash_extensions.logging import set_props
#
# app = DashProxy(__name__, suppress_callback_exceptions=True, external_stylesheets=[dbc.themes.BOOTSTRAP])
#
#
# # AccordionItem title with a clickable div
# def make_trade_item(trade_id):
#     return dbc.AccordionItem(
#         children=html.Div(id={'type': 'accord-content', 'index': trade_id}),
#         title=dbc.Row(
#             dbc.Col(
#                 html.Div(
#                     [f"Trade {trade_id}", html.Div(id={'type': 'accordion-toggle', 'index': trade_id})],
#                     # id={'type': 'accordion-toggle', 'index': trade_id},
#                     n_clicks=0,
#                     style={
#                         "cursor": "pointer",
#                         "fontWeight": "bold",
#                         "width": "100%",
#                         "height": "100%",
#                         "padding": "0.5rem 1rem",
#                         "backgroundColor": "yellow"
#                     }
#                 ),
#                 width=12
#             ),
#             className="g-0",  # no gutter
#             style={"width": "100%", "margin": "0"}
#         ),
#         item_id={"type": "active-item", "index": trade_id},
#     )
#
#
# app.layout = html.Div([
#     dbc.Accordion(
#         children=[make_trade_item(i) for i in range(1, 4)],
#         id="accordion",
#         start_collapsed=True, flush=True  # to keep multiple trades open if needed
#     )
# ])
#
#
# @app.callback(
#     Output({'type': 'accord-content', 'index': MATCH}, 'children'),
#     Input({'type': 'accordion-toggle', 'index': MATCH}, 'n_clicks'),
#     Trigger({'type': 'accordion-toggle', 'index': MATCH}, 'n_clicks'),
#     prevent_initial_call=True
# )
# def row_opened(n_clicks, trigger):
#     trade_id = ctx.triggered_id['index']
#     return [
#         dcc.Textarea(id={'type': 'notes-input', 'index': trade_id}, style={"width": "100%"}),
#         dbc.Button("Save", id={'type': 'save-btn', 'index': trade_id}, className="mt-2"),
#         html.Div(id={'type': 'save-status', 'index': trade_id}),
#         html.Div(id="clickable-div", n_clicks=0, children="clickable div")
#     ]
#
#
# @app.callback(
#     # Output({'type': 'accord-content', 'index': MATCH}, 'children'),
#     Input("accordion", "active_item"),
#     prevent_initial_call=True
# )
# def active_item(active):
#     trade_id = active["index"]
#     set_props({'type': 'accordion-toggle', 'index': trade_id}, {"n_clicks": "5"})
#
#
# @app.callback(
#     # Output("status", "children"),
#     Input("clickable-div", "n_clicks")
# )
# def on_click(n):
#     return f"Clicked {n} times"
#
#
# # Save button handler using ALL
# @app.callback(
#     Output({'type': 'save-status', 'index': MATCH}, 'children'),
#     Input({'type': 'save-btn', 'index': MATCH}, 'n_clicks'),
#     State({'type': 'notes-input', 'index': MATCH}, 'value'),
#     prevent_initial_call=True
# )
# def save_trade(n_clicks, notes):
#     set_props("clickable-div", {"n_clicks": 5})
#     trade_id = ctx.triggered_id['index']
#     print(f"Saving trade {trade_id}: {notes}")
#     return f"Saved note for trade {trade_id}"
#
#
# if __name__ == "__main__":
#     app.run(debug=False)











#
# import dash
# from dash import html, Input, Output, State, dash_table
# import dash_bootstrap_components as dbc
# import pandas as pd
#
# # Sample Data
# df = pd.DataFrame({
#     "id": [1, 2, 3],
#     "name": ["Alice", "Bob", "Charlie"],
#     "details": ["Detail 1: Lorem ipsum...", "Detail 2: Some info...", "Detail 3: More details..."]
# })
#
# app = dash.Dash(__name__, external_stylesheets=[dbc.themes.BOOTSTRAP])
#
# app.layout = html.Div([
#     dash_table.DataTable(
#         id="table",
#         columns=[{"name": "Name", "id": "name"}],  # Only showing Name in the table
#         data=df.to_dict("records"),
#         style_table={"width": "50%"},
#         row_selectable="single",  # Allows selecting a single row at a time
#         selected_rows=[]
#     ),
#     html.Div(id="details-container"),  # Where expanded row details will appear
#     dbc.Button("Click me", id="example-button", className="me-2", n_clicks=0),
#     html.Span(id="example-output", style={"verticalAlign": "middle"})
# ])
#
#
# @app.callback(
#     Output("example-output", "children"),
#     [Input("example-button", "n_clicks")]
# )
# def on_button_click(n):
#     print("helllooo")
#     if n is None:
#         return "Not clicked."
#     else:
#         return f"Clicked {n} times."
#
#
# # Callback to show/hide expanded row details
# @app.callback(
#     Output("details-container", "children"),
#     Input("table", "selected_rows"),
#     prevent_initial_call=True
# )
# def toggle_expand(selected_rows):
#     if not selected_rows:  # If no row is selected, clear details
#         return ""
#
#     row_idx = selected_rows[0]  # Get the index of the selected row
#     selected_data = df.iloc[row_idx]  # Get the corresponding row data
#
#     # Create a dynamically expanding row beneath the selected row
#     return html.Div([
#         html.Div(f"Details for {selected_data['name']}:", style={"font-weight": "bold", "margin-top": "10px"}),
#         dbc.Collapse(
#             dbc.Card(
#                 dbc.CardBody(selected_data["details"]),
#                 style={"width": "50%"}
#             ),
#             is_open=True,
#             id="collapse-content"
#         )
#     ])
#
#
# if __name__ == "__main__":
#     app.run(debug=False)

# import dash_ag_grid as dag
# from dash import Dash, html, Output, Input
# import pandas as pd
#
# app = Dash()
#
# df = pd.read_csv("https://raw.githubusercontent.com/plotly/datasets/master/ag-grid/space-mission-data.csv")
#
# rowData = [
#     {"make": "Toyota", "model": "Celica", "price": 35000},
#     {"make": "Ford", "model": "Mondeo", "price": 32000},
#     {"make": "Porsche", "model": "Boxster", "price": 72000},
# ]
#
# columnDefs = [
#     {"headerName": "Make", "field": "make", "sortable": True},
#     {"headerName": "Model", "field": "model"},
#     {"headerName": "Price", "field": "price"},
# ]
#
# df = pd.DataFrame({
#     "id": [1, 2, 3],
#     "name": ["Alice", "Bob", "Charlie"],
#     "details": ["Detail 1: Lorem ipsum...", "Detail 2: Some info...", "Detail 3: More details..."]
# })
#
# # Define column structure (without masterDetail)
# columns = [
#     {"field": "name", "headerName": "Name"},
#     {"field": "expand", "headerName": "Expand", "cellRenderer": "expandRenderer"}
# ]
#
# expand_renderer = """
# function(params) {
#     let button = document.createElement('button');
#     button.textContent = params.node.expanded ? '▼ Collapse' : '▶ Expand';
#     button.style.cursor = 'pointer';
#     button.onclick = function() {
#         params.api.applyTransaction({ add: [{ id: 'expand-' + params.node.data.id, name: '', details: params.node.data.details }], addIndex: params.node.rowIndex + 1 });
#         params.node.setExpanded(!params.node.expanded);
#     };
#     return button;
# }
# """
#
# app.layout = html.Div([
#     dag.AgGrid(
#         id="grid",
#         rowData=df.to_dict("records"),
#         columnDefs=columns,
#         defaultColDef={"resizable": True},
#         dashGridOptions={
#             "rowSelection": "single"  # ✅ Enable row selection
#         },
#         dangerously_allow_code=True,  # Allows custom JS renderer
#     ),
#     html.Div(id="details-container", style={"margin-top": "20px"})
# ])
#
#
# # Callback to display details when a row is clicked
# @app.callback(
#     Output("details-container", "children"),
#     Input("grid", "selectedRows"),
#     prevent_initial_call=True
# )
# def show_details(selected_rows):
#     if selected_rows:
#         row = selected_rows[0]  # Get the first selected row
#         return html.Div([
#             html.H4(f"Details for {row['name']}"),
#             html.P(row['details']),
#             html.Hr()
#         ])
#     return ""
#
#
# app.run(debug=False)


# import dash
# from dash import dcc, html, Input, Output, State, ctx
# import dash_table
# import pandas as pd
# import time  # Simulating a database call delay
#
# # Sample trade summary data (Replace with database queries)
# trade_data = pd.DataFrame([
#     {"trade_id": 1, "symbol": "BTC/USD", "entry_price": 50000, "exit_price": 52000},
#     {"trade_id": 2, "symbol": "ETH/USD", "entry_price": 3000, "exit_price": 3100},
#     {"trade_id": 3, "symbol": "ADA/USD", "entry_price": 2.5, "exit_price": 3.0}
# ])
#
# app = dash.Dash(__name__)
#
# app.layout = html.Div([
#     # Trade Summary Table
#     dash_table.DataTable(
#         id="trade-table",
#         columns=[
#             {"name": "Trade ID", "id": "trade_id"},
#             {"name": "Symbol", "id": "symbol"},
#             {"name": "Entry Price", "id": "entry_price"},
#             {"name": "Exit Price", "id": "exit_price"},
#         ],
#         data=trade_data.to_dict("records"),
#         style_table={"width": "100%"},
#         row_selectable="single",  # Allows selecting only one row
#     ),
#
#     # Hidden div to expand trade details when row is clicked
#     html.Div(id="trade-details", style={"margin-top": "20px"}),
# ])
#
#
# # Simulated database fetch function (replace with actual DB call)
# def fetch_trade_details(trade_id):
#     return {
#         "trade_id": trade_id,
#         "symbol": "BTC/USD" if trade_id == 1 else "ETH/USD",
#         "entry_price": 50000 if trade_id == 1 else 3000,
#         "exit_price": 52000 if trade_id == 1 else 3100,
#         "trade_volume": 1.5,  # Example additional data
#         "profit_loss": "+2000 USDT" if trade_id == 1 else "+100 USDT",
#         "chart": f"Trade {trade_id} Chart Placeholder",
#     }
#
#
# @app.callback(
#     Output("trade-details", "children"),
#     Input("trade-table", "selected_rows"),
#     State("trade-table", "data")
# )
# def display_trade_details(selected_rows, table_data):
#     if not selected_rows:
#         return html.Div("Trade selected" + str(selected_rows) + str(table_data))  # Return empty div if no selection
#
#     selected_trade = table_data[selected_rows[0]]  # Get selected trade data
#     trade_id = selected_trade["trade_id"]
#
#     # Fetch trade details from database
#     trade_info = fetch_trade_details(trade_id)
#
#     return html.Div([
#         html.H3(f"Trade Details for {trade_info['symbol']}"),
#         html.P(f"Trade ID: {trade_info['trade_id']}"),
#         html.P(f"Entry Price: {trade_info['entry_price']}"),
#         html.P(f"Exit Price: {trade_info['exit_price']}"),
#         html.P(f"Trade Volume: {trade_info['trade_volume']}"),
#         html.P(f"Profit/Loss: {trade_info['profit_loss']}"),
#         html.Div(f"", style={"border": "1px solid #ddd", "padding": "10px", "margin-top": "10px"}),
#     ], style={"border": "2px solid black", "padding": "20px", "margin-top": "20px"})
#
#
# if __name__ == "__main__":
#     app.run(debug=False)

# import dash
# import dash_bootstrap_components as dbc
# import dash_html_components as html
#
# app = dash.Dash(__name__, external_stylesheets=[dbc.themes.BOOTSTRAP])
#
# cards = [
#     dbc.Card(
#         dbc.CardBody([
#             html.H4("Card 1"),
#             html.P("Short content")
#         ]), className="h-100"
#     ),
#     dbc.Card(
#         dbc.CardBody([
#             html.H4("Card 2"),
#             html.P("This card has more text than the first one.")
#         ]), className="h-100"
#     ),
#     dbc.Card(
#         dbc.CardBody([
#             html.H4("Card 3"),
#             html.P("Very short.")
#         ], className="d-flex flex-column justify-content-center"),  # Center content
#         className="h-100"
#     ),
#     dbc.Card(
#         dbc.CardBody([
#             html.H4("Card 4"),
#             html.P("This one has the most content and will be the tallest. " * 5)
#         ]), className="h-100"
#     )
# ]
#
# app.layout = html.Div([
#     dbc.Row(
#         [dbc.Col(card, width=3) for card in cards],
#         className="g-4"  # Adds spacing between cards
#     )
# ])
#
#
#
# if __name__ == "__main__":
#     app.run(debug=False)


# # I
# # expect
# # this
# # might
# # be
# # useful
# # later
# # on, even if only
# # for myself.The answer is that the ListGroup component does return a list of ListGroupItem components that are written in JSON when collected in the callback.Therefore, whenever you append a ListGroupItem component to a ListGroup it will get added as a ListGroupItem() element, however, when you get it back it will read like a JSON.Bellow, I am showing an example from my code: string1 and string2
# # have
# # been
# # previously
# # added
# # to
# # the
# # ListGroup
# # component, however, string3
# # was
# # added in the
# # current
# # callback, therefore
# # it
# # appears as a
# # ListGroupItem
# # element
# # rather
# # than
# # a
# # JSON.
#
# [{'props': {'children': 'string1'}, 'type': 'ListGroupItem', 'namespace': 'dash_bootstrap_components'},
#  {'props': {'children': 'string2'}, 'type': 'ListGroupItem', 'namespace': 'dash_bootstrap_components'},
#  ListGroupItem('string3')]
# # My
# # final
# # version
# # for my specific callback is:
#
#
# @app.callback(
#     [Output('list-group-items', 'children')],
#     [Input('input-domain-specific-words', 'value'),
#      Input('add-button', 'n_clicks'),
#      Input('delete-button', 'n_clicks'),
#      Input('reset-button', 'n_clicks')],
#     [State('list-group-items', 'children')],
# )
# def updateWordList(word, n_clicks_add, n_clicks_delete, n_clicks_reset, listChildren):
#     ctx = dash.callback_context
#     if not ctx.triggered:
#         raise dash.exceptions.PreventUpdate
#     else:
#         button_id = ctx.triggered[0]['prop_id'].split('.')[0]
#
#     if button_id not in ['add-button', 'delete-button', 'reset-button']:
#         raise dash.exceptions.PreventUpdate
#     else:
#         if not listChildren:
#             listChildren = []
#
#         if button_id == 'delete-button':
#             for item in listChildren:
#                 if item['props']['children'] == word:
#                     listChildren.remove(item)
#
#         elif button_id == 'add-button':
#             listChildren.append(dbc.ListGroupItem(word))
#
#         elif button_id == 'reset-button':
#             print('pressed reset')
#             return [[]]
#
#     return [listChildren]


# DYNAMIC UPDATE DATEPICKER initial_visible_month

# @app.callback(
#     Output("my-paragraph", "children"),
#     Output("my-date-picker-range", "initial_visible_month"),
#     State("my-date-picker-range", "initial_visible_month"),
#     Input("my-date-picker-range", "start_date"),
#     Input("my-date-picker-range", "end_date"),
# )
# def initial_visible_month_change(initial_visible_month, start_date, end_date):
#     triggered_id = ctx.triggered_id
#
#     if triggered_id == "my-date-picker-range":
#
#         for possible_triggers in ctx.args_grouping:
#             if possible_triggers["triggered"] is True:
#
#                 if possible_triggers["property"] == "start_date":
#                     initial_visible_month = start_date
#                 elif possible_triggers["property"] == "end_date":
#                     initial_visible_month = end_date
#
#                 paragraph_text = f"initial_visible_month is {initial_visible_month} and set by {possible_triggers['property']}"
#
#                 return (paragraph_text, initial_visible_month)
#
#     paragraph_text = f"initial_visible_month is {initial_visible_month}"
#     return (paragraph_text, no_update)
