import os


def count_lines(directory, extension='.py'):
    total_lines = 0
    for root, dirs, files in os.walk(directory):
        for file in files:
            if file.endswith(extension):
                with open(os.path.join(root, file), 'r', encoding='utf-8') as f:
                    total_lines += sum(1 for line in f if line.strip())
    return total_lines


def count_lines_file(file_path):
    with open(file_path, 'r', encoding='utf-8') as f:
        return sum(1 for line in f if line.strip())


print(f"Total Python lines in Models: {count_lines('C:\\Users\\<USER>\\PycharmProjects\\journal\\models')}")
print(f"Total Python lines in services: {count_lines('C:\\Users\\<USER>\\PycharmProjects\\journal\\services')}")
print(f"Total Python lines in dash_app: {count_lines('C:\\Users\\<USER>\\PycharmProjects\\journal\\dash_app')}")
print(f"Total Python lines in cdos: {count_lines_file('C:\\Users\\<USER>\\PycharmProjects\\journal\\cdos.py')}")
print(f"Total Python lines in claude: {count_lines_file('C:\\Users\\<USER>\\PycharmProjects\\journal\\claude.py')}")
print(f"Total Python lines in helper: {count_lines_file('C:\\Users\\<USER>\\PycharmProjects\\journal\\helper.py')}")
# print(f"Total Python lines in MEMEBot: {count_lines_file('C:\\Users\\<USER>\\PycharmProjects\\journal\\MEMEBot.py')}")
print( f"Total Python lines in trades_db: {count_lines_file('C:\\Users\\<USER>\\PycharmProjects\\journal\\trades_db.py')}")
print( f"Total Python lines in trades_db_tables: {count_lines_file('C:\\Users\\<USER>\\PycharmProjects\\journal\\trades_db_tables.py')}")

# Calculate total line count across all files and directories
total_lines = (
    count_lines('C:\\Users\\<USER>\\PycharmProjects\\journal\\models') +
    count_lines('C:\\Users\\<USER>\\PycharmProjects\\journal\\services') +
    count_lines('C:\\Users\\<USER>\\PycharmProjects\\journal\\dash_app') +
    count_lines_file('C:\\Users\\<USER>\\PycharmProjects\\journal\\cdos.py') +
    count_lines_file('C:\\Users\\<USER>\\PycharmProjects\\journal\\claude.py') +
    count_lines_file('C:\\Users\\<USER>\\PycharmProjects\\journal\\helper.py') +
    # count_lines_file('C:\\Users\\<USER>\\PycharmProjects\\journal\\MEMEBot.py') +
    count_lines_file('C:\\Users\\<USER>\\PycharmProjects\\journal\\trades_db.py') +
    count_lines_file('C:\\Users\\<USER>\\PycharmProjects\\journal\\trades_db_tables.py')
)
print(f"\nTotal Python lines in project: {total_lines}")
