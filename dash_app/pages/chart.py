import dash
import plotly.graph_objects as go
from dash import html, dcc, Input, Output, State, clientside_callback
from flask import session

from services import binance as binance_service

dash.register_page(__name__, name="TradeCraft - Chart")

# Layout: Full-screen chart area with an auto-refresh interval
controls_layout = html.Div([
    dcc.Interval(id="btc-candle-refresh", interval=60 * 1000, n_intervals=0),  # refresh every 60s
    dcc.Store(id="chart-y-range", data=None),  # Store for Y-axis range
    dcc.Graph(id="btc-candle-chart", config={
        "displaylogo": False,
        "scrollZoom": True,
        "doubleClick": "reset",
        "showTips": False,
        "displayModeBar": True,
        "modeBarButtonsToRemove": [
            "toggleSpikelines",
            "select2d",
            "lasso2d",
            "zoom2d"
        ],
        "modeBarButtonsToAdd": [
            "pan2d"
        ]
    }, style={
        # "height": "calc(100vh - 20px)",
        "width": "90%",
        "height": "80%",
    })
], style={
    "padding": "0",
    "margin": "0",
    "width": "100%",
})


def layout():
    if "user_id" not in session:
        return dcc.Location(pathname="/login", id="redirect")
    return controls_layout


# Callback to load/update BTCUSDT 1H candlestick chart
@dash.callback(
    Output("btc-candle-chart", "figure"),
    [Input("btc-candle-refresh", "n_intervals")],
    [State("chart-y-range", "data")]
)
def update_btc_candles(_, stored_y_range):
    # Fetch 1-hour interval candlesticks for BTC/USDT
    df = binance_service.get_candlestick_dataframe(symbol="BTCUSDT", limit=1000)

    if df is None or df.empty:
        # Return an empty figure with annotation if no data
        fig = go.Figure()
        fig.update_xaxes(fixedrange=True)
        fig.add_annotation(text="No candlestick data available",
                           xref="paper", yref="paper", x=0.5, y=0.5, showarrow=False)
        fig.update_layout(
            xaxis=dict(visible=False),
            yaxis=dict(visible=False),
            margin=dict(l=20, r=20, t=20, b=20),
        )
        return fig

    # Build candlestick chart
    fig = go.Figure(go.Candlestick(
        x=df.index,
        open=df['open'],
        high=df['high'],
        low=df['low'],
        close=df['close'],
        name="BTC/USDT",
        hoverinfo='skip',
    ))

    # Styling consistent with existing charts (no range slider, tight margins, analysis-friendly)
    fig.update_layout(
        dragmode='zoom',  # Changed from 'pan' to 'zoom' to enable axis-specific dragging
        xaxis_rangeslider_visible=False,
        margin=dict(l=10, r=70, t=10, b=10),
        xaxis=dict(
            showgrid=True,
            gridcolor="#2a2a2a",
            showspikes=True,
            spikemode="across",
            spikesnap="cursor",
            fixedrange=False
        ),
        yaxis=dict(
            side="right",
            showgrid=True,
            gridcolor="#2a2a2a",
            fixedrange=False,  # This allows Y-axis dragging
            showspikes=True,
            spikemode="across",
            showline=True,
            linewidth=2,
            linecolor="#666",
            ticks="outside",
            tickwidth=2,
            tickcolor="#666",
            ticklen=8,
            mirror=True,
            zeroline=True,
            zerolinewidth=1,
            zerolinecolor="#888"
        ),
        hovermode=False,
        uirevision="btc-1h"
    )

    # Apply stored Y-axis range if available
    if stored_y_range and 'yaxis.range[0]' in stored_y_range and 'yaxis.range[1]' in stored_y_range:
        fig.update_layout(
            yaxis=dict(
                range=[stored_y_range['yaxis.range[0]'], stored_y_range['yaxis.range[1]']],
                side="right",
                showgrid=True,
                gridcolor="#2a2a2a",
                fixedrange=False,
                showspikes=True,
                spikemode="across",
                showline=True,
                linewidth=2,
                linecolor="#666",
                ticks="outside",
                tickwidth=2,
                tickcolor="#666",
                ticklen=8,
                mirror=True,
                zeroline=True,
                zerolinewidth=1,
                zerolinecolor="#888"
            )
        )

    return fig


# Callback to handle Y-axis drag interactions and store the range
@dash.callback(
    Output("chart-y-range", "data"),
    Input("btc-candle-chart", "relayoutData"),
    State("chart-y-range", "data"),
    prevent_initial_call=True
)
def update_y_range(relayout_data, current_range):
    """Store Y-axis range when user drags or zooms the price scale"""
    if relayout_data is None:
        return current_range

    # Check if Y-axis range was modified
    if 'yaxis.range[0]' in relayout_data and 'yaxis.range[1]' in relayout_data:
        return {
            'yaxis.range[0]': relayout_data['yaxis.range[0]'],
            'yaxis.range[1]': relayout_data['yaxis.range[1]']
        }

    # Handle autosize/reset events
    if 'yaxis.autorange' in relayout_data and relayout_data['yaxis.autorange']:
        return None

    return current_range


# Add clientside callback for smooth Y-axis dragging on price scale
clientside_callback(
    """
    function(figure) {
        if (!figure || !figure.layout) {
            return window.dash_clientside.no_update;
        }

        // Enable Y-axis dragging specifically on the price scale area
        if (figure.layout.yaxis) {
            figure.layout.yaxis.fixedrange = false;
        }

        return figure;
    }
    """,
    Output("btc-candle-chart", "figure", allow_duplicate=True),
    Input("btc-candle-chart", "figure"),
    prevent_initial_call=True
)
