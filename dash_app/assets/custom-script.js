//// Enhanced Price Scale Drag Functionality for TradeCraft Charts
//document.addEventListener('DOMContentLoaded', function() {
//
//    // Function to enhance price scale interaction
//    function enhancePriceScaleDrag() {
//        const chartContainer = document.getElementById('btc-candle-chart');
//        if (!chartContainer) return;
//
//        // Wait for Plotly to initialize
//        setTimeout(() => {
//            const plotlyDiv = chartContainer.querySelector('.js-plotly-plot');
//            if (!plotlyDiv) return;
//
//            // Variables for Y-axis dragging
//            let isDraggingY = false;
//            let startY = 0;
//            let startRange = null;
//
//            // Add event listeners for enhanced Y-axis interaction
//            plotlyDiv.addEventListener('plotly_relayout', function(eventData) {
//                // Handle Y-axis range changes
//                if (eventData && (eventData['yaxis.range[0]'] !== undefined || eventData['yaxis.range[1]'] !== undefined)) {
//                    console.log('Y-axis range updated:', eventData);
//                }
//            });
//
//            // Function to check if mouse is over Y-axis area
//            function isOverYAxis(event) {
//                const rect = plotlyDiv.getBoundingClientRect();
//                const x = event.clientX - rect.left;
//                const plotWidth = rect.width;
//                // Y-axis is on the right side, check if mouse is in the rightmost 70px
//                return x > (plotWidth - 70);
//            }
//
//            // Mouse down event
//            plotlyDiv.addEventListener('mousedown', function(event) {
//                if (isOverYAxis(event)) {
//                    isDraggingY = true;
//                    startY = event.clientY;
//
//                    // Get current Y-axis range
//                    const layout = plotlyDiv.layout;
//                    if (layout && layout.yaxis && layout.yaxis.range) {
//                        startRange = [...layout.yaxis.range];
//                    }
//
//                    event.preventDefault();
//                    event.stopPropagation();
//                }
//            });
//
//            // Mouse move event
//            plotlyDiv.addEventListener('mousemove', function(event) {
//                if (isDraggingY && startRange) {
//                    const deltaY = event.clientY - startY;
//                    const sensitivity = 0.01; // Adjust sensitivity as needed
//
//                    const rangeSize = startRange[1] - startRange[0];
//                    const adjustment = deltaY * rangeSize * sensitivity;
//
//                    const newRange = [
//                        startRange[0] + adjustment,
//                        startRange[1] + adjustment
//                    ];
//
//                    // Update the chart
//                    Plotly.relayout(plotlyDiv, {
//                        'yaxis.range': newRange
//                    });
//
//                    event.preventDefault();
//                    event.stopPropagation();
//                } else if (isOverYAxis(event)) {
//                    // Change cursor when over Y-axis
//                    plotlyDiv.style.cursor = 'ns-resize';
//                } else {
//                    plotlyDiv.style.cursor = '';
//                }
//            });
//
//            // Mouse up event
//            document.addEventListener('mouseup', function(event) {
//                if (isDraggingY) {
//                    isDraggingY = false;
//                    startY = 0;
//                    startRange = null;
//                    plotlyDiv.style.cursor = '';
//                }
//            });
//
//            // Enhance cursor feedback on Y-axis area
//            const yAxisElements = plotlyDiv.querySelectorAll('.yaxis, .yaxislayer-above');
//            yAxisElements.forEach(element => {
//                element.style.cursor = 'ns-resize';
//
//                element.addEventListener('mouseenter', function() {
//                    this.style.opacity = '0.8';
//                });
//
//                element.addEventListener('mouseleave', function() {
//                    this.style.opacity = '1';
//                });
//            });
//
//        }, 1000);
//    }
//
//    // Initialize enhancement
//    enhancePriceScaleDrag();
//
//    // Re-initialize when page content changes (for SPA navigation)
//    const observer = new MutationObserver(function(mutations) {
//        mutations.forEach(function(mutation) {
//            if (mutation.type === 'childList') {
//                enhancePriceScaleDrag();
//            }
//        });
//    });
//
//    observer.observe(document.body, {
//        childList: true,
//        subtree: true
//    });
//});
//
//// Additional utility function for smooth Y-axis transitions
//function smoothYAxisTransition(plotlyDiv, newRange) {
//    if (!plotlyDiv || !newRange) return;
//
//    Plotly.relayout(plotlyDiv, {
//        'yaxis.range': newRange,
//        'yaxis.transition': {
//            duration: 300,
//            easing: 'cubic-in-out'
//        }
//    });
//}