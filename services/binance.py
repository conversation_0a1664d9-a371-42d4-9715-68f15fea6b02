import pandas as pd
from binance.client import Client
from datetime import datetime, timezone
from decimal import Decimal
import pytz


client = Client("C1ij9R03NeJxqIONoK107iJKSpM3po37lZkj2hp1ilJ3I6ad9Lek4KPGo1f8cccI",
                "juZPs5yoxSjSqt4le3K5VuR1z6owJITSnPc4yhbsULQhTlYldcXmPgoLvpYgkS5L",
                "",
                "us")


def map_symbol_to_binance(base_symbol):
    """
    Map Coinbase futures base symbols to Binance spot symbols.

    Args:
        base_symbol (str): Base symbol from Coinbase futures (e.g., "SHB", "BTC")

    Returns:
        str: Binance symbol format (e.g., "SHIBUSDT", "BTCUSDT")
    """
    # Common symbol mappings
    symbol_map = {
        "SHB": "SHIBUSDT",
        "BTC": "BTCUSDT",
        "ETH": "ETHUSDT",
        "SOL": "SOLUSDT",
        "ADA": "ADAUSDT",
        "DOT": "DOTUSDT",
        "LINK": "LINKUSDT",
        "UNI": "UNIUSDT",
        "LTC": "LTCUSDT",
        "BCH": "BCHUSDT",
        "XRP": "XRPUSDT",
        "DOGE": "DOGEUSDT",
        "MATIC": "MATICUSDT",
        "AVAX": "AVAXUSDT",
        "ATOM": "ATOMUSDT",
        "NEAR": "NEARUSDT",
        "FTM": "FTMUSDT",
        "ALGO": "ALGOUSDT",
        "VET": "VETUSDT",
        "ICP": "ICPUSDT"
    }

    # Return mapped symbol or default to base_symbol + "USDT"
    return symbol_map.get(base_symbol.upper(), f"{base_symbol.upper()}USDT")


def get_klines_data_frame(symbol, interval, limit):
    history = client.get_klines(symbol=symbol, interval=interval, limit=limit)

    time_index_list = [timeItem[0] for timeItem in history]

    # Convert Elements to floats
    converted_history = [[float(item) for item in floatItem] for floatItem in history]

    data = pd.DataFrame(converted_history, index=time_index_list, columns=['OpenTime',
                                                                           'Open',
                                                                           'High',
                                                                           'Low',
                                                                           'Close',
                                                                           'Volume',
                                                                           'CloseTime',
                                                                           'QuoteAssetVolume',
                                                                           'NumberOfTrades',
                                                                           'TakerBuyBaseSssetBolume',
                                                                           'TakerNuyWuoteSssetBolume',
                                                                           'Ignore'])
    return data


def get_historical_price_at_timestamp(base_symbol, target_timestamp):
    """
    Get the historical price of an asset at a specific timestamp using Binance API.

    Args:
        base_symbol (str): Base symbol from Coinbase futures (e.g., "SHB", "BTC")
        target_timestamp (int): Unix timestamp in seconds

    Returns:
        Decimal: The price at the specified timestamp, or None if not found
    """
    try:
        # Map to Binance symbol format
        binance_symbol = map_symbol_to_binance(base_symbol)

        # Convert timestamp to milliseconds for Binance API
        target_timestamp_ms = target_timestamp * 1000

        # Get klines data around the target timestamp
        # Use 1-minute intervals and get a small range around the target time
        start_time = target_timestamp_ms - (5 * 60 * 1000)  # 5 minutes before
        end_time = target_timestamp_ms + (5 * 60 * 1000)    # 5 minutes after

        klines = client.get_historical_klines(
            symbol=binance_symbol,
            interval=Client.KLINE_INTERVAL_1MINUTE,
            start_str=str(start_time),
            end_str=str(end_time)
        )

        if not klines:
            print(f"No klines data found for {binance_symbol} at timestamp {target_timestamp}")
            return None

        # Find the kline closest to our target timestamp
        closest_kline = None
        min_time_diff = float('inf')

        for kline in klines:
            kline_timestamp = int(kline[0])  # Open time in milliseconds
            time_diff = abs(kline_timestamp - target_timestamp_ms)

            if time_diff < min_time_diff:
                min_time_diff = time_diff
                closest_kline = kline

        if closest_kline:
            # Return the close price of the closest kline
            close_price = float(closest_kline[4])  # Close price is at index 4
            return Decimal(str(close_price))
        else:
            print(f"No suitable kline found for {binance_symbol} at timestamp {target_timestamp}")
            return None

    except Exception as e:
        print(f"Error getting historical price for {base_symbol} at timestamp {target_timestamp}: {e}")
        return None


def get_price_at_expiration(base_symbol, expiration_date):
    """
    Get the price of an asset at its expiration time (11AM ET).

    Args:
        base_symbol (str): Base symbol from Coinbase futures (e.g., "SHB", "BTC")
        expiration_date (datetime): The expiration date (timezone-aware)

    Returns:
        Decimal: The price at expiration, or None if not found
    """
    try:
        # Convert expiration date to 11AM ET
        et_tz = pytz.timezone("America/New_York")

        # If expiration_date is naive, assume it's UTC
        if expiration_date.tzinfo is None:
            expiration_date = expiration_date.replace(tzinfo=timezone.utc)

        # Extract just the date part to avoid timezone shift issues
        expiration_date_only = expiration_date.date()

        # Create 11AM ET on the expiration date
        expiration_11am_et = et_tz.localize(
            datetime.combine(expiration_date_only, datetime.min.time().replace(hour=11))
        )

        # Convert to UTC for timestamp calculation
        expiration_11am_utc = expiration_11am_et.astimezone(timezone.utc)

        # Get timestamp in seconds
        target_timestamp = int(expiration_11am_utc.timestamp())

        print(f"Getting price for {base_symbol} at {expiration_11am_et} ET ({expiration_11am_utc} UTC)")

        return get_historical_price_at_timestamp(base_symbol, target_timestamp)

    except Exception as e:
        print(f"Error getting expiration price for {base_symbol}: {e}")
        return None



def get_candlestick_dataframe(symbol: str,
                               interval: str = Client.KLINE_INTERVAL_1HOUR,
                               limit: int = 500,
                               start_ms: int | None = None,
                               end_ms: int | None = None) -> pd.DataFrame:
    """
    Fetch historical candlestick (kline) data from Binance and return a formatted DataFrame.

    Args:
        symbol: Trading pair symbol (e.g., "BTCUSDT")
        interval: Kline interval (e.g., Client.KLINE_INTERVAL_1HOUR)
        limit: Number of candles to fetch (max per Binance API constraints)
        start_ms: Optional start time in milliseconds
        end_ms: Optional end time in milliseconds

    Returns:
        pd.DataFrame with columns: [open_time, open, high, low, close, volume, close_time,
                                    quote_asset_volume, number_of_trades, taker_buy_base_volume,
                                    taker_buy_quote_volume]
        Timestamps are converted to pandas datetime (UTC).
    """
    try:
        # Use get_klines with optional start/end to keep request simple
        klines = client.get_klines(
            symbol=symbol,
            interval=interval,
            limit=limit,
            startTime=start_ms,
            endTime=end_ms
        )

        if not klines:
            return pd.DataFrame(columns=[
                'open_time', 'open', 'high', 'low', 'close', 'volume', 'close_time',
                'quote_asset_volume', 'number_of_trades', 'taker_buy_base_volume', 'taker_buy_quote_volume'
            ])

        # Build DataFrame
        cols = [
            'open_time', 'open', 'high', 'low', 'close', 'volume', 'close_time',
            'quote_asset_volume', 'number_of_trades', 'taker_buy_base_volume', 'taker_buy_quote_volume', 'ignore'
        ]
        df = pd.DataFrame(klines, columns=cols)

        # Convert dtypes
        numeric_cols = ['open', 'high', 'low', 'close', 'volume', 'quote_asset_volume',
                        'taker_buy_base_volume', 'taker_buy_quote_volume']
        for c in numeric_cols:
            df[c] = pd.to_numeric(df[c], errors='coerce')
        df['number_of_trades'] = pd.to_numeric(df['number_of_trades'], errors='coerce').astype('Int64')

        # Convert timestamps to datetime (UTC)
        df['open_time'] = pd.to_datetime(df['open_time'], unit='ms', utc=True)
        df['close_time'] = pd.to_datetime(df['close_time'], unit='ms', utc=True)

        # Set index to open_time for time-series convenience
        df.set_index('open_time', inplace=True)
        # Drop the unused 'ignore' column
        if 'ignore' in df.columns:
            df = df.drop(columns=['ignore'])

        return df
    except Exception as e:
        # Basic error handling/logging; return empty DataFrame on failure
        print(f"Error fetching candlesticks for {symbol} @ {interval}: {e}")
        return pd.DataFrame(columns=[
            'open', 'high', 'low', 'close', 'volume', 'close_time',
            'quote_asset_volume', 'number_of_trades', 'taker_buy_base_volume', 'taker_buy_quote_volume'
        ])


# get market depth
# depth = client.get_order_book(symbol='LTCUSDT')

# history = client.get_klines(symbol='LTCUSDT', interval="1m", limit="50")

# place a test market buy order, to place an actual order use the create_order function
# order = client.order_market_sell(
#     symbol='OXTUSD',
#     quantity=100)
#
# # get all symbol prices
# prices = client.get_all_tickers()
#
# # withdraw 100 ETH
# # check docs for assumptions around withdrawals
# from binance.exceptions import BinanceAPIException
# try:
#     result = client.withdraw(
#         asset='ETH',
#         address='<eth_address>',
#         amount=100)
# except BinanceAPIException as e:
#     print(e)
# else:
#     print("Success")
#
# # fetch list of withdrawals
# withdraws = client.get_withdraw_history()
#
# # fetch list of ETH withdrawals
# eth_withdraws = client.get_withdraw_history(coin='ETH')
#
# # get a deposit address for BTC
# address = client.get_deposit_address(coin='BTC')
#
# # get historical kline data from any date range
#
# # fetch 1 minute klines for the last day up until now
# klines = client.get_historical_klines("BNBBTC", Client.KLINE_INTERVAL_1MINUTE, "1 day ago UTC")
#
# # fetch 30 minute klines for the last month of 2017
# klines = client.get_historical_klines("ETHBTC", Client.KLINE_INTERVAL_30MINUTE, "1 Dec, 2017", "1 Jan, 2018")
#
# # fetch weekly klines since it listed
# klines = client.get_historical_klines("NEOBTC", Client.KLINE_INTERVAL_1WEEK, "1 Jan, 2017")
#
# # socket manager using threads
# twm = ThreadedWebsocketManager()
# twm.start()
#
# # depth cache manager using threads
# dcm = ThreadedDepthCacheManager()
# dcm.start()
#
# def handle_socket_message(msg):
#     print(f"message type: {msg['e']}")
#     print(msg)
#
# def handle_dcm_message(depth_cache):
#     print(f"symbol {depth_cache.symbol}")
#     print("top 5 bids")
#     print(depth_cache.get_bids()[:5])
#     print("top 5 asks")
#     print(depth_cache.get_asks()[:5])
#     print("last update time {}".format(depth_cache.update_time))
#
# twm.start_kline_socket(callback=handle_socket_message, symbol='BNBBTC')
#
# dcm.start_depth_cache(callback=handle_dcm_message, symbol='ETHBTC')
#
# # replace with a current options symbol
# options_symbol = 'BTC-210430-36000-C'
# dcm.start_options_depth_cache(callback=handle_dcm_message, symbol=options_symbol)
