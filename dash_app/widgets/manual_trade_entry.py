import dash
import dash_bootstrap_components as dbc
from dash import html, dcc, Input, Output, State, ALL, MATCH, callback_context as ctx
from datetime import datetime, timezone
import pytz
from decimal import Decimal
import uuid
import json

from models.trade import Trade, TradeDirection, TradeStatus, Exchange
from models.order import Order, BuySell
from trades_db import TradesDB, get_db_cursor, get_db_connection
import helper


# ================================
# Helper Functions
# ================================

def get_current_datetime_string():
    """Get current date and time in EST timezone formatted for datetime-local input"""
    est = pytz.timezone('America/New_York')
    now = datetime.now(est)
    return now.strftime('%Y-%m-%dT%H:%M')


def create_order_form(order_id=None):
    """Create a form for a single order entry"""
    if order_id is None:
        order_id = str(uuid.uuid4())

    return dbc.Card([
        dbc.CardHeader([
            dbc.Row([
                dbc.Col(html.H6("Order Entry", className="mb-0"), width=10),
                dbc.Col(
                    dbc.Button("×",
                               id={"type": "remove-order", "index": order_id},
                               color="danger",
                               size="sm",
                               className="float-end"),
                    width=2
                )
            ])
        ]),
        dbc.CardBody([
            dbc.Row([
                dbc.Col([
                    dbc.Label("Buy/Sell"),
                    dcc.Dropdown(
                        id={"type": "order-buysell", "index": order_id},
                        options=[
                            {"label": "Buy", "value": "Buy"},
                            {"label": "Sell", "value": "Sell"}
                        ],
                        value="Buy",
                        clearable=False
                    )
                ], width=3),
                dbc.Col([
                    dbc.Label("Order Type"),
                    dcc.Dropdown(
                        id={"type": "order-type", "index": order_id},
                        options=[
                            {"label": "Market", "value": "Market"},
                            {"label": "Limit", "value": "Limit"},
                            {"label": "Stop", "value": "Stop"},
                            {"label": "Stop Limit", "value": "Stop Limit"}
                        ],
                        value="Market",
                        clearable=False
                    )
                ], width=3),
                dbc.Col([
                    dbc.Label("Quantity"),
                    dbc.Input(
                        id={"type": "order-quantity", "index": order_id},
                        type="number",
                        step="any",
                        placeholder="0.00"
                    )
                ], width=3),
                dbc.Col([
                    dbc.Label("Price"),
                    dbc.Input(
                        id={"type": "order-price", "index": order_id},
                        type="number",
                        step="any",
                        placeholder="0.00"
                    )
                ], width=3)
            ], className="mb-3"),
            dbc.Row([
                dbc.Col([
                    dbc.Label("Date & Time (EST)"),
                    dbc.Input(
                        id={"type": "order-datetime", "index": order_id},
                        type="datetime-local",
                        value=get_current_datetime_string()
                    )
                ], width=4),
                dbc.Col([
                    dbc.Label("Fee"),
                    dbc.Input(
                        id={"type": "order-fee", "index": order_id},
                        type="number",
                        step="any",
                        placeholder="0.00",
                        value=0
                    )
                ], width=2),
                dbc.Col([
                    dbc.Label("Status"),
                    dcc.Dropdown(
                        id={"type": "order-status", "index": order_id},
                        options=[
                            {"label": "Filled", "value": "Filled"},
                            {"label": "Open", "value": "Open"},
                            {"label": "Cancelled", "value": "Cancelled"}
                        ],
                        value="Filled",
                        clearable=False
                    )
                ], width=3),
                dbc.Col([
                    dbc.Label("Reduce Only"),
                    dbc.Checklist(
                        id={"type": "order-reduce", "index": order_id},
                        options=[{"label": "", "value": True}],
                        value=[],
                        inline=True,
                        style={"marginTop": "8px"}
                    )
                ], width=3)
            ])
        ])
    ], className="mb-3", id={"type": "order-card", "index": order_id})


# ================================
# Modal Layout
# ================================

modal = dbc.Modal([
    dbc.ModalHeader([
        html.Div([
            html.I(className="bi bi-plus-circle me-2", style={"fontSize": "1.5rem", "color": "#0d6efd"}),
            html.Span("Manual Trade Entry", style={"fontSize": "1.25rem", "fontWeight": "500"})
        ], className="d-flex align-items-center"),
    ]),
    dbc.ModalBody([
        # Trade Details Section
        dbc.Card([
            dbc.CardHeader(html.H6("Trade Details", className="mb-0")),
            dbc.CardBody([
                dbc.Row([
                    dbc.Col([
                        dbc.Label("Symbol"),
                        dbc.Input(
                            id="trade-symbol",
                            type="text",
                            placeholder="e.g., BTCUSDT, ETHUSDT",
                            required=True
                        )
                    ], width=4),
                    dbc.Col([
                        dbc.Label("Direction"),
                        dcc.Dropdown(
                            id="trade-direction",
                            options=[
                                {"label": "Long", "value": "Long"},
                                {"label": "Short", "value": "Short"}
                            ],
                            value="Long",
                            clearable=False
                        )
                    ], width=4),
                    dbc.Col([
                        dbc.Label("Exchange"),
                        dcc.Dropdown(
                            id="trade-exchange",
                            options=[
                                {"label": "Bybit", "value": "Bybit"},
                                {"label": "Coinbase", "value": "Coinbase"},
                                {"label": "CME-EdgeClear", "value": "CME-EdgeClear"},
                                {"label": "Binance", "value": "Binance"}
                            ],
                            value="Bybit",
                            clearable=False
                        )
                    ], width=4)
                ], className="mb-3"),
                dbc.Row([
                    dbc.Col([
                        dbc.Label("Strategy"),
                        dbc.Input(
                            id="trade-strategy",
                            type="text",
                            placeholder="Optional strategy name"
                        )
                    ], width=6),
                    dbc.Col([
                        dbc.Label("Time Frame"),
                        dbc.Input(
                            id="trade-timeframe",
                            type="text",
                            placeholder="e.g., 1H, 4H, 1D"
                        )
                    ], width=6)
                ], className="mb-3"),
                dbc.Row([
                    dbc.Col([
                        dbc.Label("Notes"),
                        dbc.Textarea(
                            id="trade-notes",
                            placeholder="Optional trade notes",
                            rows=3
                        )
                    ], width=12)
                ])
            ])
        ], className="mb-4"),

        # Orders Section
        dbc.Card([
            dbc.CardHeader([
                dbc.Row([
                    dbc.Col(html.H6("Orders", className="mb-0"), width=10),
                    dbc.Col(
                        dbc.Button("+ Add Order",
                                   id="add-order-btn",
                                   color="primary",
                                   size="sm",
                                   className="float-end"),
                        width=2
                    )
                ])
            ]),
            dbc.CardBody([
                html.Div(id="orders-container", children=[
                    create_order_form()  # Start with one order form
                ])
            ])
        ]),

        # Alert for validation messages
        html.Div(id="trade-entry-alert", className="mt-3")
    ]),
    dbc.ModalFooter([
        dbc.Button("Cancel", id="cancel-trade-entry", color="secondary", className="me-2"),
        dbc.Button("Save Trade", id="save-trade-entry", color="primary")
    ])
], id="manual-trade-modal", size="xl", is_open=False, className="manual-trade-modal")


# ================================
# Callback Functions
# ================================

def get_callbacks(app):
    """Register all callbacks for the manual trade entry modal"""

    @app.callback(
        Output("orders-container", "children"),
        Input("add-order-btn", "n_clicks"),
        State("orders-container", "children"),
        prevent_initial_call=True
    )
    def add_order_form(n_clicks, current_orders):
        """Add a new order form to the container"""
        if n_clicks:
            new_order = create_order_form()
            current_orders.append(new_order)
        return current_orders

    @app.callback(
        Output("orders-container", "children", allow_duplicate=True),
        Input({"type": "remove-order", "index": ALL}, "n_clicks"),
        State("orders-container", "children"),
        prevent_initial_call=True
    )
    def remove_order_form(n_clicks_list, current_orders):
        """Remove an order form from the container"""
        if not any(n_clicks_list) or len(current_orders) <= 1:
            return current_orders

        # Find which remove button was clicked
        ctx = dash.callback_context
        if ctx.triggered:
            button_id = ctx.triggered[0]["prop_id"]
            # Extract the index from the button ID
            button_data = json.loads(button_id.split(".")[0])
            order_id_to_remove = button_data["index"]

            # Remove the order with matching ID
            updated_orders = []
            for order in current_orders:
                if order["props"]["id"]["index"] != order_id_to_remove:
                    updated_orders.append(order)

            return updated_orders

        return current_orders

    @app.callback(
        Output("trade-entry-alert", "children"),
        Output("manual-trade-modal", "is_open", allow_duplicate=True),
        Input("save-trade-entry", "n_clicks"),
        [State("trade-symbol", "value"),
         State("trade-direction", "value"),
         State("trade-exchange", "value"),
         State("trade-strategy", "value"),
         State("trade-timeframe", "value"),
         State("trade-notes", "value"),
         State({"type": "order-buysell", "index": ALL}, "value"),
         State({"type": "order-type", "index": ALL}, "value"),
         State({"type": "order-quantity", "index": ALL}, "value"),
         State({"type": "order-price", "index": ALL}, "value"),
         State({"type": "order-datetime", "index": ALL}, "value"),
         State({"type": "order-fee", "index": ALL}, "value"),
         State({"type": "order-status", "index": ALL}, "value"),
         State({"type": "order-reduce", "index": ALL}, "value")],
        prevent_initial_call=True
    )
    def save_trade(n_clicks, symbol, direction, exchange, strategy, timeframe, notes,
                   order_buysells, order_types, order_quantities, order_prices,
                   order_datetimes, order_fees, order_statuses, order_reduces):
        """Save the manually entered trade and orders to the database"""
        if not n_clicks:
            return dash.no_update, dash.no_update

        try:
            # Validate required fields
            if not symbol:
                return dbc.Alert("Symbol is required", color="danger"), dash.no_update

            if not order_quantities or not any(order_quantities):
                return dbc.Alert("At least one order with quantity is required", color="danger"), dash.no_update

            # Create orders list
            orders = []
            for i in range(len(order_quantities)):
                if order_quantities[i] and order_prices[i]:
                    # Parse datetime
                    est = pytz.timezone('America/New_York')
                    dt = datetime.strptime(order_datetimes[i], '%Y-%m-%dT%H:%M')
                    dt_est = est.localize(dt)
                    dt_utc = dt_est.astimezone(timezone.utc)

                    order = Order(
                        id_field=None,
                        order_id=f"MANUAL_{str(uuid.uuid4())}",
                        trade_id=None,
                        created_date=dt_utc,
                        filled_date=dt_utc if order_statuses[i] == "Filled" else None,
                        symbol=symbol,
                        orderType=order_types[i],
                        orderStatus=order_statuses[i],
                        buySell=BuySell.BUY if order_buysells[i] == "Buy" else BuySell.SELL,
                        reduce=bool(order_reduces[i]),
                        price=Decimal(str(order_prices[i])),
                        fillPrice=Decimal(str(order_prices[i])) if order_statuses[i] == "Filled" else Decimal(0),
                        fee=Decimal(str(order_fees[i] or 0)),
                        quantity=Decimal(str(order_quantities[i])),
                        filledQuantity=Decimal(str(order_quantities[i])) if order_statuses[i] == "Filled" else Decimal(
                            0),
                        sierraActivity=None,
                        coinbaseOrder=None,
                        coinbaseFill=None,
                        bybitOrder=None
                    )
                    orders.append(order)

            if not orders:
                return dbc.Alert("At least one valid order is required", color="danger"), dash.no_update

            # Create trade from first order
            first_order = orders[0]
            trade = Trade.fromOrder(first_order, username="manual_entry", direction=TradeDirection(direction))

            # Mark the trade's exchange_trade_id as manual
            trade.exchange_trade_id = f"MANUAL_TRADE_{str(uuid.uuid4())}"

            # Add remaining orders to trade
            for order in orders[1:]:
                trade.trade_orders.append(order)

            # Set trade properties - find the correct Exchange enum by value
            exchange_enum = None
            for ex in Exchange:
                if ex.value == exchange:
                    exchange_enum = ex
                    break

            if exchange_enum is None:
                return dbc.Alert(f"Invalid exchange: {exchange}", color="danger"), dash.no_update

            trade.exchange = exchange_enum
            trade.strategy = strategy or ""
            trade.time_frame = timeframe or ""
            trade.notes = notes or ""

            # Update trade details
            trade.update_trade_details()

            # Save to database
            try:
                from flask import session
                trade.username = session.get("user_id", "manual_entry")
            except:
                trade.username = "manual_entry"

            cursor = get_db_cursor()
            TradesDB.saveTrade(trade, cursor)
            get_db_connection().commit()

            return dbc.Alert("Trade saved successfully!", color="success"), False

        except Exception as e:
            return dbc.Alert(f"Error saving trade: {str(e)}", color="danger"), dash.no_update

    @app.callback(
        Output("manual-trade-modal", "is_open"),
        [Input("open-manual-trade", "n_clicks"),
         Input("cancel-trade-entry", "n_clicks")],
        State("manual-trade-modal", "is_open"),
        prevent_initial_call=True
    )
    def toggle_manual_trade_modal(open_clicks, cancel_clicks, is_open):
        """Toggle the manual trade entry modal"""
        if open_clicks or cancel_clicks:
            return not is_open
        return is_open

    @app.callback(
        [Output("trade-symbol", "value"),
         Output("trade-direction", "value"),
         Output("trade-exchange", "value"),
         Output("trade-strategy", "value"),
         Output("trade-timeframe", "value"),
         Output("trade-notes", "value"),
         Output("orders-container", "children", allow_duplicate=True),
         Output("trade-entry-alert", "children", allow_duplicate=True)],
        Input("manual-trade-modal", "is_open"),
        prevent_initial_call=True
    )
    def reset_form_on_open(is_open):
        """Reset form fields when modal is opened"""
        if is_open:
            return ("", "Long", "Bybit", "", "", "", [create_order_form()], "")
        return dash.no_update, dash.no_update, dash.no_update, dash.no_update, dash.no_update, dash.no_update, dash.no_update, dash.no_update
