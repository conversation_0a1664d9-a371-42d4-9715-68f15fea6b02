import dash
from dash import html, dcc
from flask import session

dash.register_page(__name__, name="TradeCraft - Analytics")


def layout():
    if "user_id" not in session:
        return dcc.Location(pathname="/login", id="redirect")

    return controls_layout


# Define the overall layout.
controls_layout = html.Div([
    html.H1('This is our Analytics page'),
    html.Div('y ay aya ya ya.'),
], style={"padding-bottom": "20px",
          "padding-top": "20px", "width": "80%"})
