<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8" />
<meta name="viewport" content="width=device-width,initial-scale=1" />
<title>Lightweight Charts — Lines + Fib 50%</title>
<style>
  :root { --bg:#0e1116; --panel:#161b22; --text:#c9d1d9; --muted:#8b949e; --accent:#58a6ff; }
  html, body { height:100%; }
  body { margin:0; display:grid; grid-template-columns: 1fr 320px; height:100%;
         background:var(--bg); color:var(--text); font-family: ui-sans-serif, system-ui, -apple-system, Segoe UI, Roboto, Helvetica, Arial; }
  #chart { width:100%; height:100%; }
  aside { border-left:1px solid #222; background:var(--panel); display:flex; flex-direction:column; min-width:280px; }
  header, .row { padding:10px 12px; border-bottom:1px solid #222; }
  .btn { background:#222833; color:var(--text); border:1px solid #2b3340; padding:6px 10px; border-radius:8px; cursor:pointer; }
  .btn:hover { border-color:#3a4556; }
  .btn.active { outline:2px solid #58a6ff60; }
  .muted { color:var(--muted); font-size:12px; }
  ul { list-style:none; padding:0; margin:8px 12px 12px; }
  li { display:flex; justify-content:space-between; align-items:center; gap:8px; padding:6px 8px; border:1px solid #2b3340; border-radius:8px; margin-top:8px; }
  .pill { background:#0b1d33; border:1px solid #14304f; border-radius:999px; padding:2px 8px; font-variant-numeric: tabular-nums; }
  .groupTag { font-size:11px; opacity:0.7; border:1px solid #2b3340; border-radius:6px; padding:0 6px; margin-left:6px; }
  .stack { display:flex; gap:8px; flex-wrap:wrap; }
</style>
</head>
<body>
  <div id="chart"></div>

  <aside>
    <header class="stack">
      <button id="mode-normal" class="btn active" title="Normal chart viewing mode">📊 Normal</button>
      <button id="mode-line" class="btn" title="Click to place single line">➕ Line</button>
      <button id="mode-fib" class="btn" title="MouseDown set P1, drag, MouseUp set P2, auto-add 50%">∿ Fib 50%</button>
      <button id="clear" class="btn" title="Remove all lines">🧹 Clear</button>
    </header>
    <div class="row">
      <div class="muted" id="hint">Mode: Normal — chart viewing mode with pan and zoom enabled.</div>
    </div>
    <div class="row">
      <strong>Lines</strong>
      <ul id="list"></ul>
    </div>
  </aside>

  <!-- TradingView Lightweight Charts (MIT) -->
  <script src="https://unpkg.com/lightweight-charts@4.2.2/dist/lightweight-charts.standalone.production.js"></script>
  <script>
  (function(){
    // ----- Singleton guard to avoid duplicates on reload -----
    if (window.__lw_chart__) {
      try { window.__lw_chart__.remove(); } catch(e){}
      const chartEl0 = document.getElementById('chart');
      while (chartEl0.firstChild) chartEl0.removeChild(chartEl0.firstChild);
    }

    // ----- Elements -----
    const chartEl = document.getElementById('chart');
    const listEl  = document.getElementById('list');
    const btnNormal = document.getElementById('mode-normal');
    const btnLine = document.getElementById('mode-line');
    const btnFib  = document.getElementById('mode-fib');
    const btnClear= document.getElementById('clear');
    const hintEl  = document.getElementById('hint');

    // ----- Create chart -----
    const chart = window.__lw_chart__ = LightweightCharts.createChart(chartEl, {
      layout: { background: { color: '#0e1116' }, textColor: '#c9d1d9' },
      rightPriceScale: { borderVisible: false, mode: 0 },  // 0 = normal price
      timeScale: { borderVisible: false, timeVisible: true, secondsVisible: false },
      grid: { horzLines: { color: '#1f2633' }, vertLines: { color: '#1f2633' } },
      crosshair: { mode: LightweightCharts.CrosshairMode.Normal },
    });

    // Toggle chart interactions (pan/zoom) while drawing
    function setPanZoomEnabled(enabled) {
      chart.applyOptions({
        handleScroll: enabled,  // disable pressed-mouse pan
        handleScale: enabled,   // disable wheel/pinch zoom
      });
      // Optional: change cursor to signal drawing mode
      chartEl.style.cursor = enabled ? "default" : "crosshair";
    }

    const series = chart.addCandlestickSeries({
      upColor: '#26a69a', downColor: '#ef5350',
      wickUpColor: '#26a69a', wickDownColor: '#ef5350',
      borderVisible: false,
      priceFormat: { type: 'price', precision: 2, minMove: 0.01 },
    });

    // ----- Demo data -----
    const data = [];
    const t0 = Math.floor(Date.now()/1000) - 3600*24*30; // 30d hourly
    let p = 100;
    for (let i=0;i<500;i++){
      const t = t0 + i*3600;
      const o = p, h = o + Math.random()*3+1, l = o - (Math.random()*3+1), c = l + Math.random()*(h-l);
      p = c;
      data.push({ time: t, open:o, high:h, low:l, close:c });
    }
    series.setData(data);
    chart.timeScale().fitContent();

    // ----- State & helpers -----
    const pf = series.options().priceFormat || {};
    const minMove = pf.minMove ?? 0.01;
    const lines = []; // { id, price, handle, kind: 'single'|'fib1'|'fib2'|'fib50', group?:string }
    let lastPoint = null; // last crosshair point {x,y}
    let mode = 'normal';    // 'normal' | 'line' | 'fib'
    const fmt = (x) => Number(x).toLocaleString(undefined, { maximumFractionDigits: 8 });

    // For Fib mode
    let fibActive = false;
    let fibStartPrice = null;
    let tempStart = null; // temp line handles
    let tempEnd = null;

    function snap(price) {
      return Math.round(price / minMove) * minMove;
    }

    function createLine(price, opts = {}) {
      const handle = series.createPriceLine({
        price,
        color: opts.color || '#58a6ff',
        lineWidth: opts.lineWidth ?? 2,
        lineStyle: opts.lineStyle ?? 0, // 0=solid, 1=dotted, 2=dashed
        axisLabelVisible: true,
        title: opts.title ?? String(price),
      });
      const id = (crypto?.randomUUID?.() || Math.random().toString(36).slice(2));
      const rec = { id, price, handle, kind: opts.kind || 'single', group: opts.group };
      lines.push(rec);
      addListItem(rec);
      post('lw_line_added', { price, kind: rec.kind, group: rec.group });
      return rec;
    }

    function removeLineById(id) {
      const idx = lines.findIndex(l => l.id === id);
      if (idx === -1) return;
      const rec = lines[idx];
      series.removePriceLine(rec.handle);
      lines.splice(idx, 1);
      const el = document.getElementById('li-' + id);
      if (el) el.remove();
      post('lw_line_removed', { id, kind: rec.kind, group: rec.group });
    }

    function removeGroup(groupId) {
      const toRemove = lines.filter(l => l.group === groupId).map(l => l.id);
      toRemove.forEach(removeLineById);
    }

    function addListItem(rec) {
      const li = document.createElement('li');
      li.id = 'li-' + rec.id;

      const left = document.createElement('div');
      left.textContent = (rec.kind === 'single') ? 'Line @ ' :
                         (rec.kind === 'fib50') ? 'Fib 50% @ ' :
                         (rec.kind === 'fib1') ? 'Fib P1 @ ' :
                         (rec.kind === 'fib2') ? 'Fib P2 @ ' : 'Line @ ';
      const pill = document.createElement('span'); pill.className='pill'; pill.textContent = fmt(rec.price);
      left.appendChild(pill);

      if (rec.group && (rec.kind === 'fib50')) {
        const tag = document.createElement('span');
        tag.className = 'groupTag';
        tag.textContent = rec.group.slice(0,6);
        left.appendChild(tag);
      }

      const right = document.createElement('div');
      const copy = document.createElement('button'); copy.className='btn'; copy.textContent='Copy';
      copy.onclick = () => { navigator.clipboard.writeText(String(rec.price)); copy.textContent='✔'; setTimeout(()=>copy.textContent='Copy',800); };

      const del = document.createElement('button'); del.className='btn'; del.textContent='🗑️';
      del.onclick = () => {
        if (rec.group && (rec.kind === 'fib50') && (event?.shiftKey)) {
          // shift+click on the 50% row deletes the whole set
          removeGroup(rec.group);
        } else {
          removeLineById(rec.id);
        }
      };

      right.append(copy, del);
      li.append(left, right);
      listEl.appendChild(li);
    }

    function post(type, payload) {
      try { window.parent?.postMessage({ type, ...payload }, '*'); } catch(e){}
    }

    // Track last crosshair point so we always have exact y
    chart.subscribeCrosshairMove((param) => {
      if (param?.point && typeof param.point.y === 'number') {
        lastPoint = param.point;
        // live-update temp fib end line
        if (fibActive) {
          const y = lastPoint.y;
          const price = snap(series.coordinateToPrice(y));
          // re-create dynamic end line
          if (tempEnd) { try { series.removePriceLine(tempEnd); } catch(e){} tempEnd = null; }
          tempEnd = series.createPriceLine({
            price,
            color:'#cccccc', lineWidth:1, lineStyle:2, axisLabelVisible:true, title:'P2?'
          });
        }
      }
    });

    // ----- Modes -----
    function setMode(newMode){
      mode = newMode;
      btnNormal.classList.toggle('active', mode==='normal');
      btnLine.classList.toggle('active', mode==='line');
      btnFib.classList.toggle('active',  mode==='fib');

      // Enable/disable pan and zoom based on mode
      setPanZoomEnabled(mode === 'normal');

      hintEl.textContent = (mode==='normal')
          ? 'Mode: Normal — chart viewing mode with pan and zoom enabled.'
          : (mode==='line')
          ? 'Mode: Line — click anywhere to drop a horizontal line at the exact price.'
          : 'Mode: Fib 50% — mouse down to set P1, drag, mouse up to set P2. It will add P1, P2, and the midpoint 50%. Shift+click trash on the 50% row to delete the whole set.';
    }
    btnNormal.onclick = () => setMode('normal');
    btnLine.onclick = () => setMode('line');
    btnFib.onclick  = () => setMode('fib');
    setMode('normal');

    // ----- Click → single line (only in Line mode) -----
    chart.subscribeClick((param) => {
      if (mode !== 'line') return;
      if (!param?.point || typeof param.point.y !== 'number') return;
      let price = snap(series.coordinateToPrice(param.point.y));
      createLine(price, { color:'#58a6ff', kind:'single' });
      console.log('Line placed at', price);

      // Automatically switch back to normal mode after placing a line
      setMode('normal');
    });

    // ----- Fib 50%: mousedown sets P1, mouseup sets P2, auto-add 50% -----
    chartEl.addEventListener('mousedown', (e) => {
      if (mode !== 'fib') return;
      if (!lastPoint || typeof lastPoint.y !== 'number') return;

      // Disable pan/zoom while dragging Fib
      setPanZoomEnabled(false);

      fibActive = true;
      fibStartPrice = snap(series.coordinateToPrice(lastPoint.y));

      // temp start line
      tempStart = series.createPriceLine({
        price: fibStartPrice, color:'#cccccc', lineWidth:1, lineStyle:2, axisLabelVisible:true, title:'P1'
      });

      // prevent the chart from seeing this as a pan start
      e.preventDefault();
      e.stopPropagation();
    });

    const endFib = () => {
      if (!fibActive) return;
      fibActive = false;

      // finalize P2
      if (!lastPoint || typeof lastPoint.y !== 'number') {
        // cleanup temps
        if (tempStart) { try { series.removePriceLine(tempStart); } catch(e){} tempStart=null; }
        if (tempEnd)   { try { series.removePriceLine(tempEnd); } catch(e){} tempEnd=null; }
        return;
      }
      const fibEndPrice = snap(series.coordinateToPrice(lastPoint.y));
      const group = (crypto?.randomUUID?.() || Math.random().toString(36).slice(2));

      // remove temps
      if (tempStart) { try { series.removePriceLine(tempStart); } catch(e){} tempStart=null; }
      if (tempEnd)   { try { series.removePriceLine(tempEnd); } catch(e){} tempEnd=null; }

      // create permanent lines
      const p1 = createLine(fibStartPrice, { color:'#f0ad4e', lineWidth:2, kind:'fib1', group, title:'P1' });
      const p2 = createLine(fibEndPrice,   { color:'#f0ad4e', lineWidth:2, kind:'fib2', group, title:'P2' });

      const mid = snap((fibStartPrice + fibEndPrice)/2);
      const p50 = createLine(mid, { color:'#9ed0ff', lineWidth:2, kind:'fib50', group, title:'50%' });

      console.log('Fib set: P1=', fibStartPrice, ' P2=', fibEndPrice, ' 50%=', mid);
      post('lw_fib_added', { p1:fibStartPrice, p2:fibEndPrice, mid });
      fibStartPrice = null;
    };

    // mouseup on whole doc so it completes even if cursor leaves chart
    document.addEventListener('mouseup', () => {
      if (!fibActive) return;

      // Re-enable pan/zoom now that we’re done
      setPanZoomEnabled(true);

      fibActive = false;

      if (!lastPoint || typeof lastPoint.y !== 'number') {
        if (tempStart) { try { series.removePriceLine(tempStart); } catch(e){} tempStart=null; }
        if (tempEnd)   { try { series.removePriceLine(tempEnd); } catch(e){} tempEnd=null; }
        return;
      }

      const fibEndPrice = snap(series.coordinateToPrice(lastPoint.y));
      const group = (crypto?.randomUUID?.() || Math.random().toString(36).slice(2));

      if (tempStart) { try { series.removePriceLine(tempStart); } catch(e){} tempStart=null; }
      if (tempEnd)   { try { series.removePriceLine(tempEnd); } catch(e){} tempEnd=null; }

      createLine(fibStartPrice, { color:'#f0ad4e', lineWidth:2, kind:'fib1', group, title:'P1' });
      createLine(fibEndPrice,   { color:'#f0ad4e', lineWidth:2, kind:'fib2', group, title:'P2' });
      const mid = snap((fibStartPrice + fibEndPrice)/2);
      createLine(mid, { color:'#9ed0ff', lineWidth:2, kind:'fib50', group, title:'50%' });

      console.log('Fib set:', { p1:fibStartPrice, p2:fibEndPrice, mid });
      post('lw_fib_added', { p1:fibStartPrice, p2:fibEndPrice, mid });
      fibStartPrice = null;

      // Automatically switch back to normal mode after completing Fibonacci retracement
      setMode('normal');
    });

    // ----- Clear all -----
    btnClear.onclick = () => {
      // remove all lines & list
      for (const l of [...lines]) {
        try { series.removePriceLine(l.handle); } catch(e){}
      }
      lines.length = 0;
      listEl.innerHTML = '';
      post('lw_lines_cleared', {});
    };

    // ----- Responsive -----
    const ro = new ResizeObserver(entries=>{
      const { width, height } = entries[0].contentRect;
      chart.applyOptions({ width, height });
    });
    ro.observe(chartEl);
  })();
  </script>
</body>
</html>
