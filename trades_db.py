import json
import os
import sqlite3
import time

import helper
from models import trade
from models.order import Order
from models.strategy import Strategy
from trades_db_tables import TradeDB_tables


class dot_dict(dict):
    __getattr__ = dict.get
    __setattr__ = dict.__setitem__
    __delattr__ = dict.__delitem__


# Database Path Setup
BASE_DIR = os.path.dirname(os.path.abspath(__file__))
DB_DIR = os.path.normpath(os.path.join(BASE_DIR, "data"))
DB_PATH = os.path.join(DB_DIR, "trades.db")

# ✅ Ensure the database directory exists
if not os.path.exists(DB_DIR):
    os.makedirs(DB_DIR)

# ✅ Create a single shared database connection
_db_connection = None


def get_db_connection():
    """Get a single database connection, creating it if needed."""
    if not os.path.exists(DB_PATH):
        TradeDB_tables.create_db_and_tables()
    global _db_connection
    if _db_connection is None:
        _db_connection = sqlite3.connect(DB_PATH, check_same_thread=False)
    return _db_connection


def get_db_cursor():
    """Get a cursor from the shared connection."""
    return get_db_connection().cursor()


# Coinbase-Advanced-Trade API returns an OrderConfiguration object
# We will store it as json string in DB for simplicity
def object_to_dict(obj):
    """
    Recursively converts an object (including nested objects) to a dictionary.
    Handles objects with __dict__, lists, and dictionaries.
    """
    if isinstance(obj, dict):
        return {k: object_to_dict(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [object_to_dict(item) for item in obj]
    elif hasattr(obj, "__dict__"):  # Convert object attributes to dictionary
        return {k: object_to_dict(v) for k, v in obj.__dict__.items()}
    else:
        return obj  # Return primitive types (int, str, etc.) as-is


def object_to_json(obj):
    """Convert an object to a JSON string for database storage."""
    return json.dumps(object_to_dict(obj), indent=4)


class TradesDB:
    @staticmethod
    def check_db_exists():
        return os.path.exists(DB_PATH)

    @staticmethod
    def get_results(db_cursor):
        desc = [d[0] for d in db_cursor.description]
        results = [dot_dict(dict(zip(desc, res))) for res in db_cursor.fetchall()]
        return results

    # @staticmethod
    # def getProfitTimeSeries(startDate=None, endDate=None):
    #     cursor = get_db_connection().cursor()
    #     trade_data = []
    #     if startDate is not None:
    #         startDateMS = helper.dateStringToMS(startDate, date_format='%Y-%m-%d')
    #         endDateMS = helper.dateStringToMS(endDate + " 23:59:59", date_format='%Y-%m-%d %H:%M:%S')
    #         cursor.execute(
    #             'SELECT (profit - fees), timeClose FROM Trade where timeClose > ? and timeClose < ? ORDER BY '
    #             'timeClose asc',
    #             (startDateMS, endDateMS))
    #     else:
    #         cursor.execute('SELECT (profit - fees), timeClose  FROM Trade')
    #
    #     results = cursor.fetchall()
    #     if len(results) == 0:
    #         return None
    #
    #     profit = 0
    #     longs_count = 0
    #     for row in results:
    #         if row[1] is not None:
    #             profit += row[0]
    #             trade_data.append({
    #                 'profit': profit,
    #                 'timeOpen': helper.mSToDate(row[1])  # Convert MS to datetime
    #             })
    #     profit_frame = pd.DataFrame(trade_data)
    #     profit_frame.set_index('timeOpen', inplace=True)
    #     total_trades = len(profit_frame["profit"])
    #     win_rate = (np.sum(np.array(profit_frame["profit"]) > 0) /
    #                 len(profit_frame["profit"])) * 100
    #     win_rate = round(float(win_rate))
    #     return profit_frame, total_trades, win_rate

    @staticmethod
    def getBybitOrderIds():
        orderList = []
        cursor = get_db_cursor()
        cursor.execute('SELECT bybit_order_id FROM BybitOrder')
        results = TradesDB.get_results(cursor)
        for row in results:
            orderList.append(row["bybit_order_id"])
        return orderList

    @staticmethod
    def getCoinbaseOrderIds():
        orderList = []
        cursor = get_db_cursor()
        cursor.execute('SELECT coinbase_order_id FROM CoinbaseOrder')
        results = TradesDB.get_results(cursor)
        for row in results:
            orderList.append(row["coinbase_order_id"])
        return orderList

    @staticmethod
    def get_coinbase_last_order_time():
        """
        Get the oldest created_time for OPEN Coinbase orders.
        This ensures that delayed fills on old limit orders are not missed.
        If no open orders exist, falls back to the most recent order's created_time.
        """
        last_order_time = None
        cursor = get_db_cursor()

        # First try to get the oldest open order
        cursor.execute('SELECT created_time FROM CoinbaseOrder WHERE status = ? ORDER BY created_time ASC LIMIT 1', ('OPEN',))
        results = TradesDB.get_results(cursor)

        if results:
            # Found open orders, return the oldest one
            last_order_time = results[0]["created_time"]
        else:
            # No open orders found, fallback to most recent order overall
            cursor.execute('SELECT created_time FROM CoinbaseOrder ORDER BY created_time DESC LIMIT 1')
            results = TradesDB.get_results(cursor)
            if results:
                last_order_time = results[0]["created_time"]

        return last_order_time

    @staticmethod
    def get_bybit_last_order_time():
        """
        Get the oldest createdTime for OPEN Bybit orders.
        This ensures that delayed fills on old limit orders are not missed.
        If no open orders exist, falls back to the most recent order's createdTime.
        """
        last_order_time = None
        cursor = get_db_cursor()

        # First try to get the oldest open order
        cursor.execute('SELECT createdTime FROM BybitOrder WHERE orderStatus = ? ORDER BY createdTime ASC LIMIT 1', ('New',))
        results = TradesDB.get_results(cursor)

        if results:
            # Found open orders, return the oldest one
            last_order_time = results[0]["createdTime"]
        else:
            # No open orders found, fallback to most recent order overall
            cursor.execute('SELECT createdTime FROM BybitOrder ORDER BY createdTime DESC LIMIT 1')
            results = TradesDB.get_results(cursor)
            if results:
                last_order_time = results[0]["createdTime"]

        return last_order_time

    @staticmethod
    def getSierraActivityIds():
        orderList = []
        cursor = get_db_cursor()
        cursor.execute('SELECT activityId FROM SierraActivity')
        results = TradesDB.get_results(cursor)
        for row in results:
            orderList.append(row["activityId"])
        return orderList

    @staticmethod
    def getStrategiesList():
        strategies = []
        cursor = get_db_cursor()
        cursor.execute('SELECT * FROM Strategy')
        results = TradesDB.get_results(cursor)
        for row in results:
            strat = Strategy.fromRow(row=row)
            strategies.append(strat)
        return strategies

    @staticmethod
    def getTimeFrameList():
        """
        Retrieves all time frames from the database.
        
        Returns:
            list: A list of TimeFrame objects
        """
        time_frames = []
        cursor = get_db_cursor()
        cursor.execute('SELECT * FROM TimeFrame')
        results = TradesDB.get_results(cursor)

        # Import here to avoid circular imports
        from models.time_frame import TimeFrame

        for row in results:
            time_frame = TimeFrame.fromRow(row=row)
            time_frames.append(time_frame)
        return time_frames

    @staticmethod
    def getStrategyById(stratId):
        strategies = []
        cursor = get_db_cursor()
        query = "SELECT * FROM Strategy WHERE id = ?"
        cursor.execute(query, (stratId,))
        results = TradesDB.get_results(cursor)
        for row in results:
            strat = Strategy.fromRow(row=row)
            strategies.append(strat)
        return strategies[0] if strategies else None

    @staticmethod
    def getTimeFrameById(time_frame_id):
        """
        Retrieves a specific time frame by ID.

        Args:
            time_frame_id: The ID of the time frame to retrieve

        Returns:
            TimeFrame: TimeFrame object or None if not found
        """
        time_frames = []
        cursor = get_db_cursor()
        query = "SELECT * FROM TimeFrame WHERE id = ?"
        cursor.execute(query, (time_frame_id,))
        results = TradesDB.get_results(cursor)

        # Import here to avoid circular imports
        from models.time_frame import TimeFrame

        for row in results:
            time_frame = TimeFrame.fromRow(row=row)
            time_frames.append(time_frame)
        return time_frames[0] if time_frames else None

    @staticmethod
    def get_trades(exchange=None, startDate=None, endDate=None, symbol_list=None, include_open_trades=True):
        tradeList = []
        cursor = get_db_cursor()

        query = TradesDB._get_trade_select_query()
        params = []
        if exchange:
            query += " WHERE t.exchange = ?"
            params = [exchange.value]

        # Add date filtering if startDate is provided
        if startDate is not None:
            startDateMS = helper.dateStringToMS(startDate, date_format='%Y-%m-%d')
            endDateMS = helper.dateStringToMS(endDate + " 23:59:59", date_format='%Y-%m-%d %H:%M:%S')

            # Modified date condition to handle open trades
            if include_open_trades:
                # Include trades that are open (timeClose = 0 or NULL) within date range OR closed within date range
                if exchange:
                    query += " AND ((t.timeClose > ? AND t.timeClose < ?) OR ((t.timeClose = 0 OR t.timeClose IS NULL) AND t.timeOpen >= ? AND t.timeOpen <= ?))"
                else:
                    query += " WHERE ((t.timeClose > ? AND t.timeClose < ?) OR ((t.timeClose = 0 OR t.timeClose IS NULL) AND t.timeOpen >= ? AND t.timeOpen <= ?))"

                params.extend([startDateMS, endDateMS, startDateMS, endDateMS])
            else:
                # Original behavior: only include trades closed within date range
                if exchange:
                    query += " AND (t.timeClose > ? AND t.timeClose < ?)"
                else:
                    query += " WHERE (t.timeClose > ? AND t.timeClose < ?)"

                params.extend([startDateMS, endDateMS])

        # Add symbol filtering if symbol_list is provided
        if symbol_list:
            flat_symbol_list = [symbol for sublist in symbol_list for symbol in sublist]  # Flattening
            placeholders = ', '.join(['?' for _ in flat_symbol_list])
            if exchange or startDate is not None:
                query += " AND t.symbol IN ({})".format(placeholders)
            else:
                query += " WHERE t.symbol IN ({})".format(placeholders)
            params.extend(flat_symbol_list)

        # Append ORDER BY clause - Return trades in reverse chronological order
        # (newest/open trades first, oldest trades last)
        query += '''
            ORDER BY
              CASE
                WHEN t.timeClose = 0 OR t.timeClose IS NULL THEN 0  -- Prioritize open trades (timeClose = 0 or NULL)
                ELSE 1                                               -- Then closed trades
              END,
              t.lastUpdate DESC,  -- Within open trades, sort by most recent update first
              t.timeClose DESC,   -- Within closed trades, sort by most recent close first
              t.timeOpen DESC;    -- Finally, sort by most recent open time first
        '''

        cursor.execute(query, params)
        results = TradesDB.get_results(cursor)
        if len(results) == 0:
            return tradeList
        for row in results:
            order = Order.fromRow(row=row)
            # Update to use t_id instead of id for the trade ID
            th_trade = next((t for t in tradeList if t.id_field == row["t_id"]), None)
            if th_trade is not None:
                th_trade.trade_orders.append(order)
            else:
                tradeList.append(trade.Trade.fromDBRow(row=row, order=order))
        return tradeList

    @staticmethod
    def get_trade_by_id(trade_id):
        """ Retrieves a specific trade by its ID from the database. """
        cursor = get_db_cursor()

        query = TradesDB._get_trade_select_query() + " WHERE t.id = ?"
        cursor.execute(query, (trade_id,))

        results = TradesDB.get_results(cursor)
        if not results:
            return None  # Return None if no trade is found

        # Create the trade object from the first matching row
        row = results[0]
        th_trade = trade.Trade.fromDBRow(row=row, order=Order.fromRow(row=row))

        # Add additional orders related to the trade
        for row in results[1:]:
            th_trade.trade_orders.append(Order.fromRow(row=row))

        return th_trade

    @staticmethod
    def _get_trade_select_query():
        """Returns the standard SELECT query for trades up to the WHERE clause."""
        return '''
            SELECT 
                t.id AS t_id, 
                t.exchange_trade_id,
                t.status,
                t.tradeQty,
                t.openQty,
                t.timeOpen,
                t.lastUpdate,
                t.timeClose,
                t.duration,
                t.direction,
                t.symbol,
                t.chartLink,
                t.notes,
                t.notional,
                t.leverage,
                t.avgOpenPrice,
                t.avgClosePrice,
                t.riskPercent,
                t.accountBalance,
                t.riskAmt,
                t.profit,
                t.fees,
                t.exchange,
                t.strategy,
                t.time_frame,
                t.username,
                o.id AS o_id,
                o.order_id,
                o.created_date,
                o.filled_date,
                o.symbol AS o_symbol,
                o.orderType,
                o.orderStatus,
                o.buySell,
                o.reduce,
                o.price,
                o.fillPrice,
                o.quantity,
                o.filledQuantity,
                o.fee,
                o.trade_id
            FROM Trade t
            INNER JOIN Orders o ON t.id = o.trade_id
        '''

    @staticmethod
    def get_open_trades_by_exchange(exchange):
        """
        Get all open trades for a specific exchange with proper exchange-specific order data.

        Args:
            exchange: Exchange enum value

        Returns:
            list: List of Trade objects that are currently open for the specified exchange
        """
        from models.trade import Exchange

        tradeList = []
        cursor = get_db_cursor()

        # Get basic trade and order data
        query = TradesDB._get_trade_select_query()
        query += " WHERE t.exchange = ? AND (t.status = 'OPEN' OR t.timeClose = 0 OR t.timeClose IS NULL)"
        query += " ORDER BY t.lastUpdate DESC, t.timeOpen DESC"

        cursor.execute(query, (exchange.value,))
        results = TradesDB.get_results(cursor)

        if len(results) == 0:
            return tradeList

        # Process each row and load exchange-specific order data
        for row in results:
            order = Order.fromRow(row=row)

            # Load exchange-specific order data based on the exchange
            if exchange == Exchange.BYBIT:
                order.bybitOrder = TradesDB._load_bybit_order(cursor, row["o_id"])
            elif exchange == Exchange.COINBASE:
                order.coinbaseOrder = TradesDB._load_coinbase_order(cursor, row["o_id"])
                order.coinbaseFill = TradesDB._load_coinbase_fill(cursor, row["o_id"])
            elif exchange == Exchange.EDGECME:
                order.sierraActivity = TradesDB._load_sierra_activity(cursor, row["o_id"])

            # Check if we already have this trade in our list
            th_trade = next((t for t in tradeList if t.id_field == row["t_id"]), None)
            if th_trade is not None:
                # Add this order to the existing trade
                th_trade.trade_orders.append(order)
            else:
                # Create a new trade from this row
                tradeList.append(trade.Trade.fromDBRow(row=row, order=order))

        return tradeList

    @staticmethod
    def _load_bybit_order(cursor, order_id):
        """Load BybitOrder data for a given order_id"""
        cursor.execute("SELECT * FROM BybitOrder WHERE order_id = ?", (order_id,))
        results = TradesDB.get_results(cursor)
        if results:
            row = results[0]
            # Create a BybitOrder object from the database row
            bybit_data = {
                "orderId": row["bybit_order_id"],
                "orderLinkId": row["orderLinkId"],
                "blockTradeId": row["blockTradeId"],
                "symbol": row["symbol"],
                "price": row["price"],
                "qty": row["qty"],
                "side": row["side"],
                "isLeverage": row["isLeverage"],
                "positionIdx": row["positionIdx"],
                "orderStatus": row["orderStatus"],
                "createType": row["createType"],
                "cancelType": row["cancelType"],
                "rejectReason": row["rejectReason"],
                "avgPrice": row["avgPrice"],
                "leavesQty": row["leavesQty"],
                "leavesValue": row["leavesValue"],
                "cumExecQty": row["cumExecQty"],
                "cumExecValue": row["cumExecValue"],
                "cumExecFee": row["cumExecFee"],
                "timeInForce": row["timeInForce"],
                "orderType": row["orderType"],
                "stopOrderType": row["stopOrderType"],
                "orderIv": row["orderIv"],
                "triggerPrice": row["triggerPrice"],
                "takeProfit": row["takeProfit"],
                "stopLoss": row["stopLoss"],
                "tpslMode": row["tpslMode"],
                "tpLimitPrice": row["tpLimitPrice"],
                "slLimitPrice": row["slLimitPrice"],
                "tpTriggerBy": row["tpTriggerBy"],
                "slTriggerBy": row["slTriggerBy"],
                "triggerDirection": row["triggerDirection"],
                "triggerBy": row["triggerBy"],
                "lastPriceOnCreated": row["lastPriceOnCreated"],
                "reduceOnly": row["reduceOnly"],
                "closeOnTrigger": row["closeOnTrigger"],
                "placeType": row["placeType"],
                "smpType": row["smpType"],
                "smpGroup": row["smpGroup"],
                "smpOrderId": row["smpOrderId"],
                "createdTime": row["createdTime"],
                "updatedTime": row["updatedTime"]
            }
            from models.bybit_order import BybitOrder
            return BybitOrder(bybit_data)
        return None

    @staticmethod
    def _load_coinbase_order(cursor, order_id):
        """Load CoinbaseOrder data for a given order_id"""
        cursor.execute("SELECT * FROM CoinbaseOrder WHERE order_id = ?", (order_id,))
        results = TradesDB.get_results(cursor)
        if results:
            row = results[0]
            from models.coinbase_order import CoinbaseOrder
            return CoinbaseOrder(row)
        return None

    @staticmethod
    def _load_coinbase_fill(cursor, order_id):
        """Load CoinbaseFill data for a given order_id"""
        cursor.execute("SELECT * FROM CoinbaseFill WHERE order_id = ?", (order_id,))
        results = TradesDB.get_results(cursor)
        if results:
            row = results[0]
            from models.coinbase_fill import CoinbaseFill
            return CoinbaseFill(row)
        return None

    @staticmethod
    def _load_sierra_activity(cursor, order_id):
        """Load SierraActivity data for a given order_id"""
        cursor.execute("SELECT * FROM SierraActivity WHERE order_id = ?", (order_id,))
        results = TradesDB.get_results(cursor)
        if results:
            row = results[0]
            from models.sierra_activity import SierraActivity
            return SierraActivity(row)
        return None

    @staticmethod
    def clear_trade_data():
        """
        Clear all trade-related data from the database while preserving Users, Strategy, and TimeFrame tables.
        This includes: Trade, Orders, BybitOrder, CoinbaseOrder, CoinbaseFill, SierraActivity, and _strat_trade_relations.

        Returns:
            int: Number of tables cleared
        """
        cursor = get_db_cursor()
        connection = get_db_connection()

        try:
            # Begin transaction
            connection.execute("BEGIN TRANSACTION")

            # List of tables to clear (in order to respect foreign key constraints)
            tables_to_clear = [
                "BybitOrder",
                "CoinbaseOrder",
                "CoinbaseFill",
                "SierraActivity",
                "Orders",
                "Trade",
                "_strat_trade_relations"
            ]

            cleared_count = 0
            for table in tables_to_clear:
                try:
                    cursor.execute(f"DELETE FROM {table}")
                    cleared_count += 1
                    print(f"✅ Cleared {table} table")
                except Exception as e:
                    print(f"❌ Error clearing {table}: {e}")

            # Reset auto-increment sequences for cleared tables
            cursor.execute("DELETE FROM sqlite_sequence WHERE name IN ('Trade', 'Orders', 'BybitOrder', 'CoinbaseOrder', 'CoinbaseFill', 'SierraActivity')")
            print("✅ Reset auto-increment sequences")

            # Commit transaction
            connection.execute("COMMIT")
            print(f"🎉 Successfully cleared {cleared_count} tables")
            return cleared_count

        except Exception as e:
            # Rollback on error
            connection.execute("ROLLBACK")
            print(f"❌ Error clearing database: {e}")
            raise

    @staticmethod
    def get_exchange_names():
        exchanges = []
        cursor = get_db_cursor()
        cursor.execute("SELECT DISTINCT exchange FROM Trade WHERE exchange IS NOT NULL")
        exchanges = [row[0] for row in cursor.fetchall()]

        return exchanges

    @staticmethod
    def get_symbols_list():
        symbols = []
        cursor = get_db_cursor()

        # Query to get distinct symbols within the date range
        cursor.execute("""
            SELECT DISTINCT symbol FROM Trade WHERE symbol IS NOT NULL""")

        symbols = [row[0] for row in cursor.fetchall()]

        return symbols

    @staticmethod
    def deleteStrategyById(stratId):
        cursor = get_db_cursor()
        cursor.execute('''DELETE FROM Strategy WHERE id=?''',
                       (stratId,))
        get_db_connection().commit()

    @staticmethod
    def oldestSavedOrder():
        getTime = None
        cursor = get_db_cursor()
        cursor.execute('SELECT updatedTime FROM BybitOrder ORDER BY updatedTime ASC limit 5')
        rows = cursor.fetchall()
        for row in rows:
            getTime = row[0]
        return getTime

    @staticmethod
    def newestSavedOrder():
        getTime = None
        cursor = get_db_cursor()
        cursor.execute('SELECT createdTime FROM BybitOrder ORDER BY createdTime DESC limit 5')
        rows = cursor.fetchall()
        for row in rows:
            getTime = row[0]
        return getTime

    @staticmethod
    def updateStrategy(strategy):
        cursor = get_db_cursor()
        cursor.execute('''UPDATE Strategy SET name=?, description=?, notes=?, images=?, 
                                    modifiedDate=? WHERE id=?''',
                       (strategy.name, strategy.description, strategy.notes, strategy.images,
                        helper.date_to_ms(helper.get_now_date()), strategy.strat_id))
        get_db_connection().commit()

    @staticmethod
    def updateTrade(t, cursor=None):
        cursor.execute(
            'UPDATE Trade SET status = ?, tradeQty = ?, openQty = ?, timeOpen = ?, lastUpdate = ?, timeClose = ?, '
            'chartLink = ?, notes = ?, notional = ?, leverage = ?, avgOpenPrice = ?, avgClosePrice = ?, '
            'riskPercent = ?, riskAmt = ?, profit = ?, fees = ?, duration = ?, direction = ?, exchange_trade_id = ? where id = ?',
            (t.status.name, helper.store_decimal(t.tradeQty), helper.store_decimal(t.openQty),
             helper.date_to_ms(t.timeOpen), helper.date_to_ms(t.lastUpdate), helper.date_to_ms(t.timeClose),
             None, None, helper.store_decimal(t.notional), None, helper.store_decimal(t.avgOpenPrice),
             helper.store_decimal(t.avgClosePrice), helper.store_decimal(t.riskPercent),
             helper.store_decimal(t.riskAmt),
             helper.store_decimal(t.profit), helper.store_decimal(t.fees), t.duration, t.direction.value,
             t.exchange_trade_id,
             t.id_field))

        TradesDB.update_trade_orders(cursor, t)

    @staticmethod
    def update_trade_orders(cursor, t):
        for order in t.trade_orders:
            if order.id_field is None:
                order.trade_id = t.id_field
                TradesDB.saveOrder(cursor, od=order)

        # Loop over unfilled_orders and call saveOrder on each that do not have an id_field value
        for order in t.unfilled_orders:
            if order.id_field is None:
                order.trade_id = t.id_field
                TradesDB.saveOrder(cursor, od=order)

    @staticmethod
    def saveOrder(cursor, od: Order):
        cursor.execute('''INSERT INTO Orders (order_id, created_date, filled_date, symbol, orderType, orderStatus, buySell, reduce, price,
                                fillPrice, quantity, filledQuantity, trade_id, fee)
                                 VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)''',
                       (od.order_id, helper.date_to_ms(od.created_date), helper.date_to_ms(od.filled_date), od.symbol,
                        od.orderType,
                        od.orderStatus, od.buySell.value, int(od.reduce), helper.store_decimal(od.price),
                        helper.store_decimal(od.fillPrice), helper.store_decimal(od.quantity),
                        helper.store_decimal(od.filledQuantity),
                        od.trade_id, helper.store_decimal(od.fee)))

        od.id_field = cursor.lastrowid

        # Save Activity if we have one
        if od.sierraActivity:
            od.sierraActivity.orderId = od.id_field
            TradesDB.saveActivity(cursor, act=od.sierraActivity)
        elif od.coinbaseFill:
            od.coinbaseFill.orderId = od.id_field
            TradesDB.saveCoinbaseFill(cursor, fill=od.coinbaseFill)
        elif od.coinbaseOrder:
            od.coinbaseOrder.order_id = od.id_field
            TradesDB.saveCoinbaseOrder(cursor, cbo=od.coinbaseOrder)
        elif od.bybitOrder:
            od.bybitOrder.orderId = od.id_field
            TradesDB.saveBybitOrder(cursor, order=od.bybitOrder)

    @staticmethod
    def saveBybitOrder(cursor, order, commit=True):
        cursor.execute('''INSERT INTO BybitOrder (
                        bybit_order_id, orderLinkId, blockTradeId, symbol,
                        price, qty, side, isLeverage, positionIdx, orderStatus,
                        createType, cancelType, rejectReason, avgPrice, leavesQty,
                        leavesValue, cumExecQty, cumExecValue, cumExecFee, timeInForce,
                        orderType, stopOrderType, orderIv, triggerPrice, takeProfit,
                        stopLoss, tpslMode, tpLimitPrice, slLimitPrice, tpTriggerBy,
                        slTriggerBy, triggerDirection, triggerBy, lastPriceOnCreated,
                        reduceOnly, closeOnTrigger, placeType, smpType, smpGroup,
                        smpOrderId, createdTime, updatedTime, order_id)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?,
                               ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?,
                               ?, ?)''',
                       (order.bybit_order_id, order.orderLinkId, order.blockTradeId, order.symbol,
                        helper.store_decimal(order.price), helper.store_decimal(order.qty), order.side,
                        order.isLeverage, order.positionIdx, order.orderStatus,
                        order.createType, order.cancelType, order.rejectReason, helper.store_decimal(order.avgPrice),
                        helper.store_decimal(order.leavesQty),
                        helper.store_decimal(order.leavesValue), helper.store_decimal(order.cumExecQty),
                        helper.store_decimal(order.cumExecValue), helper.store_decimal(order.cumExecFee),
                        order.timeInForce,
                        order.orderType, order.stopOrderType, order.orderIv, helper.store_decimal(order.triggerPrice),
                        helper.store_decimal(order.takeProfit),
                        helper.store_decimal(order.stopLoss), order.tpslMode, helper.store_decimal(order.tpLimitPrice),
                        helper.store_decimal(order.slLimitPrice), order.tpTriggerBy,
                        order.slTriggerBy, order.triggerDirection, order.triggerBy,
                        helper.store_decimal(order.lastPriceOnCreated),
                        int(order.reduceOnly), int(order.closeOnTrigger), order.placeType, order.smpType,
                        order.smpGroup,
                        order.smpOrderId, helper.date_to_ms(order.createdTime), helper.date_to_ms(order.updatedTime), order.orderId))

    @staticmethod
    def saveActivity(cursor, act):
        cursor.execute('''INSERT INTO SierraActivity (orderId, activityId, activityType, date, transDate, symbol, orderActionSource,
                                quantity, orderType, buySell, price, price2, internalOrderId, serviceOrderId,
                                orderStatus, exchangeOrderId, fillPrice, filledQuantity, tradeAccount, openClose, parentInternalOrderId,
                                positionQuantity, fillExecutionServiceId, highDuringPosition, lowDuringPosition, note, accountBalance,
                                clientOrderId, timeInForce, isAutomated) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?,
                                ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)''',
                       (act.orderId, act.activityId, act.activityType, helper.date_to_ms(act.date),
                        helper.date_to_ms(act.transDate), act.symbol,
                        act.orderActionSource,
                        helper.store_decimal(act.quantity), act.orderType, act.buySell, helper.store_decimal(act.price),
                        helper.store_decimal(act.price2),
                        act.internalOrderId, act.serviceOrderId,
                        act.orderStatus, act.exchangeOrderId, helper.store_decimal(act.fillPrice),
                        helper.store_decimal(act.filledQuantity),
                        act.tradeAccount, act.openClose, act.parentInternalOrderId,
                        helper.store_decimal(act.positionQuantity), act.fillExecutionServiceId,
                        helper.store_decimal(act.highDuringPosition), helper.store_decimal(act.lowDuringPosition),
                        act.note, helper.store_decimal(act.accountBalance),
                        act.clientOrderId, act.timeInForce, act.isAutomated))

    @staticmethod
    def saveCoinbaseOrder(cursor, cbo, commit=True):
        insert_query = '''
            INSERT INTO CoinbaseOrder (
                coinbase_order_id, attached_order_configuration, attached_order_id, average_filled_price, cancel_message,
                client_order_id, completion_percentage, created_time, edit_history, fee, filled_size, 
                filled_value, is_liquidation, last_fill_time, leverage, margin_type, number_of_fills, order_configuration, 
                order_placement_source, order_type, originating_order_id, outstanding_hold_amount, pending_cancel, 
                product_id, product_type, reject_message, reject_reason, retail_portfolio_id, settled, side, 
                size_in_quote, size_inclusive_of_fees, status, time_in_force, total_fees, total_value_after_fees, trigger_status, 
                user_id, order_id
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            '''

        # Prepare the values from the cbo object
        values = (
            cbo.coinbase_order_id, cbo.attached_order_configuration, cbo.attached_order_id,
            helper.store_decimal(cbo.average_filled_price), cbo.cancel_message,
            cbo.client_order_id, helper.store_decimal(cbo.completion_percentage), helper.date_to_ms(cbo.created_time),
            object_to_json(cbo.edit_history),
            helper.store_decimal(cbo.fee), helper.store_decimal(cbo.filled_size),
            helper.store_decimal(cbo.filled_value),
            cbo.is_liquidation, helper.date_to_ms(cbo.last_fill_time), helper.store_decimal(cbo.leverage),
            cbo.margin_type,
            helper.store_decimal(cbo.number_of_fills), object_to_json(cbo.order_configuration),
            cbo.order_placement_source, cbo.order_type, cbo.originating_order_id, cbo.outstanding_hold_amount,
            cbo.pending_cancel, cbo.product_id, cbo.product_type, cbo.reject_message, cbo.reject_reason,
            cbo.retail_portfolio_id,
            cbo.settled, cbo.side, cbo.size_in_quote, cbo.size_inclusive_of_fees, cbo.status, cbo.time_in_force,
            helper.store_decimal(cbo.total_fees), helper.store_decimal(cbo.total_value_after_fees),
            cbo.trigger_status, cbo.user_id, cbo.order_id
        )
        # Execute the query
        cursor.execute(insert_query, values)

    @staticmethod
    def saveCoinbaseFill(cursor, fill):
        cursor.execute('''INSERT INTO CoinbaseOrder (order_id, commission, entry_id, liquidity_indicator, coinbase_order_id, price, product_id,
                                retail_portfolio_id, sequence_timestamp, side, size, size_in_quote, trade_id, trade_time, trade_type, user_id)
                                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)''',
                       (fill.order_id, helper.store_decimal(fill.commission), fill.entry_id, fill.liquidity_indicator,
                        fill.coinbase_order_id, helper.store_decimal(fill.price), fill.product_id,
                        fill.retail_portfolio_id, helper.date_to_ms(fill.sequence_timestamp), fill.side,
                        helper.store_decimal(fill.size), helper.store_decimal(fill.size_in_quote),
                        fill.trade_id, helper.date_to_ms(fill.trade_time), fill.trade_type, fill.user_id))

    @staticmethod
    def save_new_strategy(strategy):
        cursor = get_db_cursor()
        cursor.execute('''INSERT INTO Strategy (name, display_name, description, notes, images, createdDate, modifiedDate)
                                VALUES (?, ?, ?, ?, ?, ?, ?)''',
                       (strategy.name, strategy.display_name, strategy.description, strategy.notes,
                        strategy.images, helper.date_to_ms(strategy.created_date),
                        helper.date_to_ms(strategy.modified_date)))
        get_db_connection().commit()

    @staticmethod
    def saveTrade(t, cursor=None):
        if cursor is None:
            cursor = get_db_cursor()
        cursor.execute('''INSERT INTO Trade (status, tradeQty, openQty, timeOpen, lastUpdate, timeClose,
                                symbol, chartLink, notes, notional, leverage, notes, avgOpenPrice, avgClosePrice,
                                riskPercent, accountBalance, riskAmt, profit, fees, exchange, duration, direction, username, exchange_trade_id)
                                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)''',
                       (t.status.name, helper.store_decimal(t.tradeQty), helper.store_decimal(t.openQty),
                        helper.date_to_ms(t.timeOpen),
                        helper.date_to_ms(t.lastUpdate), helper.date_to_ms(t.timeClose),
                        t.symbol, None, None, helper.store_decimal(t.notional), None,
                        t.notes,
                        helper.store_decimal(t.avgOpenPrice),
                        helper.store_decimal(t.avgClosePrice),
                        helper.store_decimal(t.riskPercent),
                        helper.store_decimal(t.accountBalance),
                        helper.store_decimal(t.riskAmt),
                        helper.store_decimal(t.profit),
                        helper.store_decimal(t.fees),
                        t.exchange.value, t.duration,
                        t.direction.value, t.username, t.exchange_trade_id))

        # Set new ID to trade
        t.id_field = cursor.lastrowid
        TradesDB.update_trade_orders(cursor, t)

    @staticmethod
    def save_trade_note(trade_id, new_note):
        now_str = helper.formatDate(helper.get_now_date())
        line = f"[{now_str}] {new_note}"

        # Fetch current notes
        cursor = get_db_cursor()
        cursor.execute("SELECT notes FROM Trade WHERE id = ?", (str(trade_id),))
        row = cursor.fetchone()
        current_notes = row[0] if row and row[0] else ""

        # Append new line
        updated_notes = f"{current_notes.strip()}\n{line}".strip()

        cursor.execute("UPDATE Trade SET notes = ? WHERE id = ?", (updated_notes, str(trade_id)))
        get_db_connection().commit()
        return updated_notes

    @staticmethod
    def delete_trade_note(trade_id, note_index):
        """
        Delete a specific note from a trade's notes by its index.

        Args:
            trade_id: The ID of the trade
            note_index: The 0-based index of the note to delete

        Returns:
            str: The updated notes string after deletion
        """
        # Fetch current notes
        cursor = get_db_cursor()
        cursor.execute("SELECT notes FROM Trade WHERE id = ?", (str(trade_id),))
        row = cursor.fetchone()
        current_notes = row[0] if row and row[0] else ""

        if not current_notes.strip():
            return ""

        # Split notes into individual lines
        note_lines = [line.strip() for line in current_notes.split('\n') if line.strip()]

        # Check if the index is valid
        if 0 <= note_index < len(note_lines):
            # Remove the note at the specified index
            note_lines.pop(note_index)

        # Rejoin the remaining notes
        updated_notes = '\n'.join(note_lines)

        # Update the database
        cursor.execute("UPDATE Trade SET notes = ? WHERE id = ?", (updated_notes, str(trade_id)))
        get_db_connection().commit()
        return updated_notes

    @staticmethod
    def save_trade_strategy(strat_names, trade_id=None, exchange_trade_id=None):
        """
        Associates strategies with a trade by either trade_id or exchange_trade_id.
        
        Args:
            trade_id: The internal database ID of the trade (used if exchange_trade_id is None)
            strat_names: A strategy name or list of strategy names to associate with the trade
            exchange_trade_id: Optional. The exchange-specific trade ID. If provided, this will be used
                              to look up the trade instead of trade_id
                              
        Returns:
            None
        """
        if not isinstance(strat_names, list):
            strat_names = [strat_names]

        cursor = get_db_cursor()

        if all(isinstance(s, int) or (isinstance(s, str) and s.isdigit()) for s in strat_names):
            # These are strategy IDs - use them directly
            strat_ids = [str(s) for s in strat_names]
        else:
            # These are strategy names - look up their IDs
            placeholder = ",".join("?" for _ in strat_names)
            cursor.execute(f"SELECT id FROM Strategy WHERE name IN ({placeholder})", strat_names)
            strat_ids = [str(row[0]) for row in cursor.fetchall()]

        # Get current strategies - determine which ID to use for lookup
        if exchange_trade_id is not None:
            # Look up by exchange_trade_id
            cursor.execute("SELECT id, strategy FROM Trade WHERE exchange_trade_id = ?", (str(exchange_trade_id),))
            row = cursor.fetchone()
            if not row:
                # Trade isn't found with this exchange_trade_id
                return False
            # Get the internal trade_id for the update operation
            internal_trade_id = row[0]
            # current_ids = [s.strip() for s in str(row[1] or "").split(",") if s.strip()]
        else:
            # Use the provided trade_id directly
            cursor.execute("SELECT strategy FROM Trade WHERE id = ?", (str(trade_id),))
            row = cursor.fetchone()
            if not row:
                # Trade isn't found with this trade_id
                return False
            internal_trade_id = trade_id
            # current_ids = [s.strip() for s in str(row[0] or "").split(",") if s.strip()]

        values = ",".join(map(str, strat_ids))
        if values == "":
            values = None

        cursor.execute(
            "UPDATE Trade SET strategy = ? WHERE id = ?",
            (values, str(internal_trade_id))
        )
        get_db_connection().commit()
        return True

    @staticmethod
    def delete_trade_strategy(trade_id, strat_id):
        """
        Removes a specific strategy association from a trade.
        
        Args:
            trade_id: The ID of the trade to update
            strat_id: The ID of the strategy to remove from the trade
        
        Returns:
            bool: True if successful, False if the trade or strategy wasn't found
        """
        cursor = get_db_cursor()

        # Get current strategy list
        cursor.execute("SELECT strategy FROM Trade WHERE id = ?", (str(trade_id),))
        row = cursor.fetchone()

        if not row:
            # Trade not found
            return False

        current_strategy = row[0]

        # If no strategies are associated, nothing to remove
        if not current_strategy:
            return True

        # Parse the comma-separated list of strategy IDs
        strategy_ids = [s.strip() for s in current_strategy.split(',') if s.strip()]

        # Remove the specified strategy ID if it exists
        if str(strat_id) in strategy_ids:
            strategy_ids.remove(str(strat_id))

            # Update the database with the new list
            updated_strategy = ','.join(strategy_ids)
            cursor.execute(
                "UPDATE Trade SET strategy = ? WHERE id = ?",
                (updated_strategy, str(trade_id))
            )
            get_db_connection().commit()

        return True

    @staticmethod
    def save_trade_time_frame(time_frames, trade_id=None, exchange_trade_id=None):
        """
        Updates the time frames for a trade by either trade_id or exchange_trade_id.
        
        Args:
            time_frames: A time frame value or list of time frame values to save (e.g., "1m", "5m", "1h", etc.)
            trade_id: The internal database ID of the trade (used if exchange_trade_id is None)
            exchange_trade_id: Optional. The exchange-specific trade ID. If provided, this will be used
                              to look up the trade instead of trade_id
                              
        Returns:
            Boolean indicating success
        """
        try:
            # Convert to list if not already
            if not isinstance(time_frames, list):
                time_frames = [time_frames]

            cursor = get_db_cursor()

            if all(isinstance(s, int) or (isinstance(s, str) and s.isdigit()) for s in time_frames):
                # These are strategy IDs - use them directly
                time_frame_ids = [str(s) for s in time_frames]
            else:
                # These are strategy names - look up their IDs
                placeholder = ",".join("?" for _ in time_frames)
                cursor.execute(f"SELECT id FROM TimeFrame WHERE name IN ({placeholder})", time_frames)
                time_frame_ids = [str(row[0]) for row in cursor.fetchall()]
            # Get the internal trade ID
            if exchange_trade_id is not None:
                # Look up by exchange_trade_id
                cursor.execute("SELECT id FROM Trade WHERE exchange_trade_id = ?", (str(exchange_trade_id),))
                row = cursor.fetchone()
                if not row:
                    # Trade isn't found with this exchange_trade_id
                    return False
                # Get the internal trade_id for the update operation
                internal_trade_id = row[0]
            elif trade_id is not None:
                # Use the provided trade_id directly
                internal_trade_id = trade_id
            else:
                # Neither ID was provided
                return False

            # Join time frames with commas
            time_frames_str = ",".join(time_frame_ids)
            if time_frames_str == "":
                time_frames_str = None

            # Update the trade with the time frames
            cursor.execute("UPDATE Trade SET time_frame = ? WHERE id = ?", (time_frames_str, str(internal_trade_id)))
            get_db_connection().commit()
            return cursor.rowcount > 0
        except Exception as e:
            print(f"Error saving time frames for trade: {e}")
            return False

    @staticmethod
    def add_time_frame(name, display_name, description=None, username=None):
        """
        Adds a new time frame to the database if it doesn't already exist.
        
        Args:
            name: The time frame identifier (e.g., "13m")
            display_name: Human-readable name (e.g., "13 Minutes")
            description: Optional description
            username: Optional username of the creator
            
        Returns:
            int: ID of the new or existing time frame
        """
        cursor = get_db_cursor()

        # Check if time frame already exists
        cursor.execute("SELECT id FROM TimeFrame WHERE name = ?", (name,))
        existing = cursor.fetchone()

        if existing:
            return existing[0]

        # Find the highest sort_order
        cursor.execute("SELECT MAX(sort_order) FROM TimeFrame")
        max_sort = cursor.fetchone()[0] or 0

        # Get current timestamp
        current_time = int(time.time() * 1000)

        # Insert new time frame
        cursor.execute(
            """INSERT INTO TimeFrame 
               (name, display_name, description, sort_order, is_active, createdDate, modifiedDate, username) 
               VALUES (?, ?, ?, ?, 1, ?, ?, ?)""",
            (name, display_name, description, max_sort + 10, current_time, current_time, username)
        )

        get_db_connection().commit()
        return cursor.lastrowid

    @staticmethod
    def save_trade_notes(notes, trade_id=None, exchange_trade_id=None):
        """
        Updates the notes for a trade by either trade_id or exchange_trade_id.
        This method replaces existing notes rather than appending to them.

        Args:
            notes: The notes text to save (replaces existing notes)
            trade_id: The internal database ID of the trade (used if exchange_trade_id is None)
            exchange_trade_id: Optional. The exchange-specific trade ID. If provided, this will be used
                              to look up the trade instead of trade_id

        Returns:
            Boolean indicating success
        """
        try:
            cursor = get_db_cursor()

            # Get the internal trade ID
            if exchange_trade_id is not None:
                # Look up by exchange_trade_id
                cursor.execute("SELECT id FROM Trade WHERE exchange_trade_id = ?", (str(exchange_trade_id),))
                row = cursor.fetchone()
                if not row:
                    # Trade isn't found with this exchange_trade_id
                    return False
                # Get the internal trade_id for the update operation
                internal_trade_id = row[0]
            elif trade_id is not None:
                # Use the provided trade_id directly
                internal_trade_id = trade_id
            else:
                # Neither ID was provided
                return False

            # Update the trade with the notes (direct replacement)
            cursor.execute("UPDATE Trade SET notes = ? WHERE id = ?", (notes, str(internal_trade_id)))
            get_db_connection().commit()
            return cursor.rowcount > 0
        except Exception as e:
            print(f"Error saving notes for trade: {e}")
            return False

    @staticmethod
    def batch_update_trades(trades):
        """Update multiple trades in a single transaction"""
        connection = get_db_connection()
        cursor = connection.cursor()
        try:
            # Begin transaction
            connection.execute("BEGIN TRANSACTION")
            updated_count = 0
            for t in trades:
                TradesDB.updateTrade(t, cursor)
                updated_count += 1
            # Commit transaction
            connection.execute("COMMIT")
            return updated_count
        except Exception as e:
            # Rollback on error
            connection.execute("ROLLBACK")
            print(f"Error in batch_update_trades: {e}")
            raise
